import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api, { apiHelpers } from '../../utils/api';

const initialState = {
  posts: [],
  isLoading: false,
  error: null
};

export const fetchUserPosts = createAsyncThunk(
  'posts/fetchUserPosts',
  async (userId, { rejectWithValue }) => {
    try {
      // Placeholder for fetching user posts - will be implemented when backend is ready
      return [];
    } catch (error) {
      return rejectWithValue(apiHelpers.handleError(error));
    }
  }
);



const postSlice = createSlice({
  name: 'post',
  initialState,
  reducers: {
    setPosts: (state, action) => {
      state.posts = action.payload;
    },
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    addPost: (state, action) => {
      state.posts.unshift(action.payload);
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch user posts
      .addCase(fetchUserPosts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserPosts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.posts = action.payload;
      })
      .addCase(fetchUserPosts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  }
});

export const { setPosts, setLoading, setError, addPost, clearError } = postSlice.actions;
export default postSlice.reducer; 