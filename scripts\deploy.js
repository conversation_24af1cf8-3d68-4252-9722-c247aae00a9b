#!/usr/bin/env node

/**
 * Production Deployment Script for NeTuArk
 * 
 * This script handles:
 * - Environment validation
 * - Build optimization
 * - Asset compression
 * - Cache busting
 * - Deployment to various platforms
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  buildDir: 'dist',
  environments: ['staging', 'production'],
  platforms: ['netlify', 'vercel', 'aws', 'docker'],
  requiredEnvVars: [
    'VITE_API_BASE_URL',
    'VITE_APP_URL'
  ]
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Utility functions
const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const error = (message) => {
  log(`❌ Error: ${message}`, 'red');
  process.exit(1);
};

const success = (message) => {
  log(`✅ ${message}`, 'green');
};

const info = (message) => {
  log(`ℹ️  ${message}`, 'blue');
};

const warning = (message) => {
  log(`⚠️  ${message}`, 'yellow');
};

// Validate environment
const validateEnvironment = (env) => {
  info(`Validating ${env} environment...`);
  
  const envFile = `.env.${env}`;
  if (!fs.existsSync(envFile)) {
    error(`Environment file ${envFile} not found`);
  }
  
  // Load environment variables
  require('dotenv').config({ path: envFile });
  
  // Check required environment variables
  const missing = CONFIG.requiredEnvVars.filter(varName => !process.env[varName]);
  if (missing.length > 0) {
    error(`Missing required environment variables: ${missing.join(', ')}`);
  }
  
  success(`Environment ${env} validated`);
};

// Clean build directory
const cleanBuild = () => {
  info('Cleaning build directory...');
  
  if (fs.existsSync(CONFIG.buildDir)) {
    execSync(`rm -rf ${CONFIG.buildDir}`, { stdio: 'inherit' });
  }
  
  success('Build directory cleaned');
};

// Run build
const runBuild = (env) => {
  info(`Building for ${env} environment...`);
  
  try {
    execSync(`npm run build`, {
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: env === 'production' ? 'production' : 'development' }
    });
    success('Build completed successfully');
  } catch (error) {
    error('Build failed');
  }
};

// Optimize assets
const optimizeAssets = () => {
  info('Optimizing assets...');
  
  const distPath = path.resolve(CONFIG.buildDir);
  
  if (!fs.existsSync(distPath)) {
    error('Build directory not found. Run build first.');
  }
  
  // Compress images (if imagemin is available)
  try {
    execSync('npx imagemin "dist/images/*" --out-dir=dist/images --plugin=imagemin-mozjpeg --plugin=imagemin-pngquant', {
      stdio: 'pipe'
    });
    success('Images optimized');
  } catch (err) {
    warning('Image optimization skipped (imagemin not available)');
  }
  
  // Generate gzip files for static assets
  try {
    execSync('find dist -type f \\( -name "*.js" -o -name "*.css" -o -name "*.html" \\) -exec gzip -k {} \\;', {
      stdio: 'pipe'
    });
    success('Gzip compression completed');
  } catch (err) {
    warning('Gzip compression failed');
  }
};

// Generate build info
const generateBuildInfo = (env) => {
  info('Generating build information...');
  
  const buildInfo = {
    environment: env,
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    commit: getGitCommit(),
    branch: getGitBranch(),
    buildNumber: process.env.BUILD_NUMBER || 'local',
    nodeVersion: process.version,
    platform: process.platform
  };
  
  fs.writeFileSync(
    path.join(CONFIG.buildDir, 'build-info.json'),
    JSON.stringify(buildInfo, null, 2)
  );
  
  success('Build information generated');
};

// Get git commit hash
const getGitCommit = () => {
  try {
    return execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
  } catch {
    return 'unknown';
  }
};

// Get git branch
const getGitBranch = () => {
  try {
    return execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
  } catch {
    return 'unknown';
  }
};

// Deploy to platform
const deployToPlatform = (platform, env) => {
  info(`Deploying to ${platform} (${env})...`);
  
  switch (platform) {
    case 'netlify':
      deployToNetlify(env);
      break;
    case 'vercel':
      deployToVercel(env);
      break;
    case 'aws':
      deployToAWS(env);
      break;
    case 'docker':
      buildDockerImage(env);
      break;
    default:
      error(`Unknown platform: ${platform}`);
  }
};

// Netlify deployment
const deployToNetlify = (env) => {
  try {
    const siteId = env === 'production' ? process.env.NETLIFY_SITE_ID : process.env.NETLIFY_STAGING_SITE_ID;
    execSync(`netlify deploy --prod --dir=${CONFIG.buildDir} --site=${siteId}`, { stdio: 'inherit' });
    success('Deployed to Netlify');
  } catch (error) {
    error('Netlify deployment failed');
  }
};

// Vercel deployment
const deployToVercel = (env) => {
  try {
    const prodFlag = env === 'production' ? '--prod' : '';
    execSync(`vercel --yes ${prodFlag}`, { stdio: 'inherit' });
    success('Deployed to Vercel');
  } catch (error) {
    error('Vercel deployment failed');
  }
};

// AWS S3 deployment
const deployToAWS = (env) => {
  try {
    const bucket = env === 'production' ? process.env.AWS_S3_BUCKET : process.env.AWS_S3_STAGING_BUCKET;
    execSync(`aws s3 sync ${CONFIG.buildDir} s3://${bucket} --delete`, { stdio: 'inherit' });
    
    // Invalidate CloudFront cache
    const distributionId = env === 'production' ? process.env.AWS_CLOUDFRONT_DISTRIBUTION_ID : process.env.AWS_CLOUDFRONT_STAGING_DISTRIBUTION_ID;
    if (distributionId) {
      execSync(`aws cloudfront create-invalidation --distribution-id ${distributionId} --paths "/*"`, { stdio: 'inherit' });
    }
    
    success('Deployed to AWS S3');
  } catch (error) {
    error('AWS deployment failed');
  }
};

// Docker image build
const buildDockerImage = (env) => {
  try {
    const tag = `netuark:${env}-${Date.now()}`;
    execSync(`docker build -t ${tag} .`, { stdio: 'inherit' });
    success(`Docker image built: ${tag}`);
  } catch (error) {
    error('Docker build failed');
  }
};

// Main deployment function
const deploy = () => {
  const args = process.argv.slice(2);
  const env = args[0] || 'staging';
  const platform = args[1] || 'netlify';
  
  if (!CONFIG.environments.includes(env)) {
    error(`Invalid environment: ${env}. Valid options: ${CONFIG.environments.join(', ')}`);
  }
  
  if (!CONFIG.platforms.includes(platform)) {
    error(`Invalid platform: ${platform}. Valid options: ${CONFIG.platforms.join(', ')}`);
  }
  
  log(`🚀 Starting deployment to ${platform} (${env})`, 'cyan');
  
  try {
    validateEnvironment(env);
    cleanBuild();
    runBuild(env);
    optimizeAssets();
    generateBuildInfo(env);
    deployToPlatform(platform, env);
    
    log(`🎉 Deployment completed successfully!`, 'green');
    log(`Environment: ${env}`, 'blue');
    log(`Platform: ${platform}`, 'blue');
    log(`Build directory: ${CONFIG.buildDir}`, 'blue');
  } catch (error) {
    error(`Deployment failed: ${error.message}`);
  }
};

// Run deployment if called directly
if (require.main === module) {
  deploy();
}

module.exports = { deploy };
