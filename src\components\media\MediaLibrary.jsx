import { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  PhotoIcon,
  VideoCameraIcon,
  DocumentIcon,
  TrashIcon,
  EyeIcon,
  DownloadIcon,
  ShareIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { Button } from '../ui/Button';

const MediaLibrary = ({ isOpen, onClose, onSelectMedia, allowMultiple = false }) => {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [viewMode, setViewMode] = useState('grid'); // grid or list
  const fileInputRef = useRef(null);

  // Mock media files
  const [mediaFiles] = useState([
    {
      id: 1,
      name: 'product-launch.jpg',
      type: 'image',
      size: '2.4 MB',
      url: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400',
      uploadDate: '2024-01-15',
      dimensions: '1920x1080',
      tags: ['product', 'launch', 'marketing']
    },
    {
      id: 2,
      name: 'team-meeting.mp4',
      type: 'video',
      size: '45.2 MB',
      url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      uploadDate: '2024-01-14',
      duration: '2:34',
      tags: ['team', 'meeting', 'corporate']
    },
    {
      id: 3,
      name: 'brand-guidelines.pdf',
      type: 'document',
      size: '1.8 MB',
      url: '/documents/brand-guidelines.pdf',
      uploadDate: '2024-01-13',
      pages: 24,
      tags: ['brand', 'guidelines', 'design']
    },
    {
      id: 4,
      name: 'office-space.jpg',
      type: 'image',
      size: '3.1 MB',
      url: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=400',
      uploadDate: '2024-01-12',
      dimensions: '2048x1365',
      tags: ['office', 'workspace', 'interior']
    },
    {
      id: 5,
      name: 'customer-testimonial.mp4',
      type: 'video',
      size: '67.8 MB',
      url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4',
      uploadDate: '2024-01-11',
      duration: '1:45',
      tags: ['customer', 'testimonial', 'review']
    },
    {
      id: 6,
      name: 'social-media-kit.zip',
      type: 'document',
      size: '12.4 MB',
      url: '/documents/social-media-kit.zip',
      uploadDate: '2024-01-10',
      tags: ['social', 'media', 'kit', 'templates']
    }
  ]);

  const filteredFiles = mediaFiles.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         file.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesFilter = filterType === 'all' || file.type === filterType;
    return matchesSearch && matchesFilter;
  });

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    files.forEach(file => {
      // Simulate file upload
      toast.success(`${file.name} uploaded successfully!`);
    });

    // Reset input
    event.target.value = '';
  };

  const handleFileSelect = (file) => {
    if (allowMultiple) {
      setSelectedFiles(prev => {
        const isSelected = prev.find(f => f.id === file.id);
        if (isSelected) {
          return prev.filter(f => f.id !== file.id);
        } else {
          return [...prev, file];
        }
      });
    } else {
      onSelectMedia?.(file);
      onClose?.();
    }
  };

  const handleSelectMultiple = () => {
    onSelectMedia?.(selectedFiles);
    onClose?.();
  };

  const handleDeleteFile = (fileId, event) => {
    event.stopPropagation();
    // Simulate file deletion
    toast.success('File deleted successfully!');
  };

  const handleDownload = (file, event) => {
    event.stopPropagation();
    // Simulate file download
    toast.success(`Downloading ${file.name}...`);
  };

  const getFileIcon = (type) => {
    switch (type) {
      case 'image':
        return PhotoIcon;
      case 'video':
        return VideoCameraIcon;
      case 'document':
        return DocumentIcon;
      default:
        return DocumentIcon;
    }
  };

  const formatFileSize = (size) => {
    return size;
  };

  const MediaCard = ({ file }) => {
    const isSelected = selectedFiles.find(f => f.id === file.id);
    const Icon = getFileIcon(file.type);

    return (
      <motion.div
        layout
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className={`relative group cursor-pointer rounded-lg border-2 transition-all ${
          isSelected
            ? 'border-primary-500 bg-primary-500/10'
            : 'border-gray-700 hover:border-gray-600'
        }`}
        onClick={() => handleFileSelect(file)}
      >
        {/* Preview */}
        <div className="aspect-square rounded-t-lg overflow-hidden bg-gray-800">
          {file.type === 'image' ? (
            <img
              src={file.url}
              alt={file.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Icon className="h-12 w-12 text-gray-400" />
            </div>
          )}
          
          {/* Overlay */}
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                // Handle preview
              }}
              className="p-2 bg-white/20 rounded-full hover:bg-white/30 transition-colors"
            >
              <EyeIcon className="h-5 w-5 text-white" />
            </button>
            <button
              onClick={(e) => handleDownload(file, e)}
              className="p-2 bg-white/20 rounded-full hover:bg-white/30 transition-colors"
            >
              <DownloadIcon className="h-5 w-5 text-white" />
            </button>
            <button
              onClick={(e) => handleDeleteFile(file.id, e)}
              className="p-2 bg-red-500/20 rounded-full hover:bg-red-500/30 transition-colors"
            >
              <TrashIcon className="h-5 w-5 text-red-400" />
            </button>
          </div>
        </div>

        {/* File Info */}
        <div className="p-3">
          <h4 className="text-sm font-medium text-white truncate">{file.name}</h4>
          <div className="flex items-center justify-between mt-1">
            <span className="text-xs text-gray-400">{formatFileSize(file.size)}</span>
            <span className="text-xs text-gray-400">{file.uploadDate}</span>
          </div>
          
          {/* Additional Info */}
          <div className="mt-2 text-xs text-gray-400">
            {file.type === 'image' && file.dimensions && (
              <span>{file.dimensions}</span>
            )}
            {file.type === 'video' && file.duration && (
              <span>{file.duration}</span>
            )}
            {file.type === 'document' && file.pages && (
              <span>{file.pages} pages</span>
            )}
          </div>

          {/* Tags */}
          {file.tags && file.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {file.tags.slice(0, 2).map((tag, index) => (
                <span
                  key={index}
                  className="px-1.5 py-0.5 bg-gray-700 text-xs text-gray-300 rounded"
                >
                  {tag}
                </span>
              ))}
              {file.tags.length > 2 && (
                <span className="px-1.5 py-0.5 bg-gray-700 text-xs text-gray-300 rounded">
                  +{file.tags.length - 2}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Selection Indicator */}
        {allowMultiple && (
          <div className={`absolute top-2 right-2 w-5 h-5 rounded-full border-2 ${
            isSelected
              ? 'bg-primary-500 border-primary-500'
              : 'border-gray-400 bg-transparent'
          }`}>
            {isSelected && (
              <div className="w-full h-full flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            )}
          </div>
        )}
      </motion.div>
    );
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={(e) => e.target === e.currentTarget && onClose?.()}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="bg-background rounded-lg shadow-xl border border-gray-800 w-full max-w-6xl h-[80vh] flex flex-col"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-800">
          <div className="flex items-center space-x-3">
            <PhotoIcon className="h-6 w-6 text-primary-400" />
            <div>
              <h2 className="text-xl font-semibold text-white">Media Library</h2>
              <p className="text-sm text-gray-400">
                {allowMultiple ? 'Select multiple files' : 'Select a file'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Toolbar */}
        <div className="flex items-center justify-between p-4 border-b border-gray-800">
          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search files..."
                className="pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            {/* Filter */}
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Files</option>
              <option value="image">Images</option>
              <option value="video">Videos</option>
              <option value="document">Documents</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            {/* Upload Button */}
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*,video/*,.pdf,.doc,.docx"
              onChange={handleFileUpload}
              className="hidden"
            />
            <Button
              onClick={() => fileInputRef.current?.click()}
              className="flex items-center space-x-2"
            >
              <PlusIcon className="h-4 w-4" />
              <span>Upload</span>
            </Button>

            {/* Select Button (for multiple selection) */}
            {allowMultiple && selectedFiles.length > 0 && (
              <Button
                onClick={handleSelectMultiple}
                variant="primary"
              >
                Select {selectedFiles.length} file{selectedFiles.length !== 1 ? 's' : ''}
              </Button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {filteredFiles.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-400">
              <PhotoIcon className="h-16 w-16 mb-4" />
              <h3 className="text-lg font-medium mb-2">No files found</h3>
              <p className="text-sm text-center">
                {searchQuery || filterType !== 'all'
                  ? 'Try adjusting your search or filter'
                  : 'Upload your first file to get started'
                }
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              <AnimatePresence>
                {filteredFiles.map((file) => (
                  <MediaCard key={file.id} file={file} />
                ))}
              </AnimatePresence>
            </div>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default MediaLibrary;
