import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Async thunks
export const createCommunity = createAsyncThunk(
  'community/create',
  async (communityData, { rejectWithValue }) => {
    try {
      const response = await axios.post('/api/communities', communityData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const fetchCommunities = createAsyncThunk(
  'community/fetchAll',
  async ({ page = 1, limit = 10, search = '' }, { rejectWithValue }) => {
    try {
      const response = await axios.get('/api/communities', {
        params: { page, limit, search },
      });
      return response.data;
    } catch (error) {
      // Fallback to mock data if API is not available
      if (error.code === 'ECONNREFUSED' || error.response?.status === 404) {
        console.warn('API not available, using mock community data');

        const mockCommunities = [
          {
            _id: 'mock-community-1',
            name: 'Tech Enthusiasts',
            slug: 'tech-enthusiasts',
            description: 'A community for technology lovers and innovators',
            category: 'Technology',
            memberCount: 1250,
            postCount: 89,
            isJoined: true,
            isAdmin: false,
            avatar: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=100&h=100&fit=crop',
            banner: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=200&fit=crop',
            privacy: 'public',
            createdAt: '2024-01-01T00:00:00.000Z'
          },
          {
            _id: 'mock-community-2',
            name: 'Creative Minds',
            slug: 'creative-minds',
            description: 'Share your creative projects and get inspired',
            category: 'Art & Design',
            memberCount: 890,
            postCount: 156,
            isJoined: false,
            isAdmin: false,
            avatar: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=100&h=100&fit=crop',
            banner: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=800&h=200&fit=crop',
            privacy: 'public',
            createdAt: '2024-01-02T00:00:00.000Z'
          },
          {
            _id: 'mock-community-3',
            name: 'Fitness Journey',
            slug: 'fitness-journey',
            description: 'Motivation and tips for your fitness goals',
            category: 'Health & Fitness',
            memberCount: 2100,
            postCount: 234,
            isJoined: true,
            isAdmin: true,
            avatar: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=100&h=100&fit=crop',
            banner: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=200&fit=crop',
            privacy: 'public',
            createdAt: '2024-01-03T00:00:00.000Z'
          }
        ].filter(community =>
          !search || community.name.toLowerCase().includes(search.toLowerCase()) ||
          community.description.toLowerCase().includes(search.toLowerCase())
        );

        return {
          communities: mockCommunities,
          totalPages: 1,
          currentPage: page,
          totalCommunities: mockCommunities.length
        };
      }

      return rejectWithValue(error.response?.data || 'Failed to fetch communities');
    }
  }
);

export const fetchCommunity = createAsyncThunk(
  'community/fetchOne',
  async (slug, { rejectWithValue }) => {
    try {
      const response = await axios.get(`/api/communities/${slug}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const updateCommunity = createAsyncThunk(
  'community/update',
  async ({ slug, data }, { rejectWithValue }) => {
    try {
      const response = await axios.put(`/api/communities/${slug}`, data);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const joinCommunity = createAsyncThunk(
  'community/join',
  async (slug, { rejectWithValue }) => {
    try {
      const response = await axios.post(`/api/communities/${slug}/join`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const leaveCommunity = createAsyncThunk(
  'community/leave',
  async (slug, { rejectWithValue }) => {
    try {
      const response = await axios.post(`/api/communities/${slug}/leave`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const banUser = createAsyncThunk(
  'community/banUser',
  async ({ slug, userId, reason }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`/api/communities/${slug}/ban`, { userId, reason });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const unbanUser = createAsyncThunk(
  'community/unbanUser',
  async ({ slug, userId }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`/api/communities/${slug}/unban`, { userId });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const removePost = createAsyncThunk(
  'community/removePost',
  async ({ slug, postId, reason }, { rejectWithValue }) => {
    try {
      const response = await axios.delete(`/api/communities/${slug}/posts/${postId}`, { data: { reason } });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const reportContent = createAsyncThunk(
  'community/reportContent',
  async ({ slug, contentId, type, reason }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`/api/communities/${slug}/report`, { contentId, type, reason });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const fetchReports = createAsyncThunk(
  'community/fetchReports',
  async (slug, { rejectWithValue }) => {
    try {
      const response = await axios.get(`/api/communities/${slug}/reports`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const deleteCommunity = createAsyncThunk(
  'community/delete',
  async (slug, { rejectWithValue }) => {
    try {
      await axios.delete(`/api/communities/${slug}`);
      return slug;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete community');
    }
  }
);

const initialState = {
  communities: [],
  currentCommunity: null,
  isLoading: false,
  error: null,
  totalPages: 1,
  currentPage: 1,
  bannedUsers: [],
  reports: [],
  isModerating: false,
};

const communitySlice = createSlice({
  name: 'community',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentCommunity: (state) => {
      state.currentCommunity = null;
    },
    clearReports: (state) => {
      state.reports = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Create Community
      .addCase(createCommunity.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createCommunity.fulfilled, (state, action) => {
        state.isLoading = false;
        state.communities.unshift(action.payload);
      })
      .addCase(createCommunity.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || 'Failed to create community';
      })

      // Fetch Communities
      .addCase(fetchCommunities.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCommunities.fulfilled, (state, action) => {
        state.isLoading = false;
        state.communities = action.payload.communities;
        state.totalPages = action.payload.totalPages;
        state.currentPage = action.payload.currentPage;
      })
      .addCase(fetchCommunities.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || 'Failed to fetch communities';
      })

      // Fetch Single Community
      .addCase(fetchCommunity.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCommunity.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentCommunity = action.payload;
      })
      .addCase(fetchCommunity.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || 'Failed to fetch community';
      })

      // Update Community
      .addCase(updateCommunity.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateCommunity.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentCommunity = action.payload;
        const index = state.communities.findIndex(
          (c) => c._id === action.payload._id
        );
        if (index !== -1) {
          state.communities[index] = action.payload;
        }
      })
      .addCase(updateCommunity.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || 'Failed to update community';
      })

      // Join Community
      .addCase(joinCommunity.fulfilled, (state, action) => {
        if (state.currentCommunity) {
          state.currentCommunity.members.push(action.payload);
          state.currentCommunity.memberCount += 1;
        }
        const index = state.communities.findIndex(
          (c) => c._id === action.payload.communityId
        );
        if (index !== -1) {
          state.communities[index].members.push(action.payload);
          state.communities[index].memberCount += 1;
        }
      })

      // Leave Community
      .addCase(leaveCommunity.fulfilled, (state, action) => {
        if (state.currentCommunity) {
          state.currentCommunity.members = state.currentCommunity.members.filter(
            (m) => m.userId !== action.payload.userId
          );
          state.currentCommunity.memberCount -= 1;
        }
        const index = state.communities.findIndex(
          (c) => c._id === action.payload.communityId
        );
        if (index !== -1) {
          state.communities[index].members = state.communities[index].members.filter(
            (m) => m.userId !== action.payload.userId
          );
          state.communities[index].memberCount -= 1;
        }
      })

      // Ban User
      .addCase(banUser.pending, (state) => {
        state.isModerating = true;
        state.error = null;
      })
      .addCase(banUser.fulfilled, (state, action) => {
        state.isModerating = false;
        state.bannedUsers.push(action.payload);
        if (state.currentCommunity) {
          state.currentCommunity.members = state.currentCommunity.members.filter(
            m => m.userId !== action.payload.userId
          );
          state.currentCommunity.memberCount -= 1;
        }
      })
      .addCase(banUser.rejected, (state, action) => {
        state.isModerating = false;
        state.error = action.payload?.message || 'Failed to ban user';
      })

      // Unban User
      .addCase(unbanUser.pending, (state) => {
        state.isModerating = true;
        state.error = null;
      })
      .addCase(unbanUser.fulfilled, (state, action) => {
        state.isModerating = false;
        state.bannedUsers = state.bannedUsers.filter(
          b => b.userId !== action.payload.userId
        );
      })
      .addCase(unbanUser.rejected, (state, action) => {
        state.isModerating = false;
        state.error = action.payload?.message || 'Failed to unban user';
      })

      // Remove Post
      .addCase(removePost.pending, (state) => {
        state.isModerating = true;
        state.error = null;
      })
      .addCase(removePost.fulfilled, (state, action) => {
        state.isModerating = false;
        if (state.currentCommunity) {
          state.currentCommunity.posts = state.currentCommunity.posts.filter(
            p => p._id !== action.payload.postId
          );
        }
      })
      .addCase(removePost.rejected, (state, action) => {
        state.isModerating = false;
        state.error = action.payload?.message || 'Failed to remove post';
      })

      // Report Content
      .addCase(reportContent.fulfilled, (state, action) => {
        state.reports.unshift(action.payload);
      })

      // Fetch Reports
      .addCase(fetchReports.pending, (state) => {
        state.isModerating = true;
        state.error = null;
      })
      .addCase(fetchReports.fulfilled, (state, action) => {
        state.isModerating = false;
        state.reports = action.payload;
      })
      .addCase(fetchReports.rejected, (state, action) => {
        state.isModerating = false;
        state.error = action.payload?.message || 'Failed to fetch reports';
      })

      // Delete Community
      .addCase(deleteCommunity.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteCommunity.fulfilled, (state, action) => {
        state.isLoading = false;
        state.communities = state.communities.filter(
          (community) => community.slug !== action.payload
        );
        state.currentCommunity = null;
      })
      .addCase(deleteCommunity.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const {
  clearError,
  clearCurrentCommunity,
  clearReports,
} = communitySlice.actions;

export default communitySlice.reducer; 