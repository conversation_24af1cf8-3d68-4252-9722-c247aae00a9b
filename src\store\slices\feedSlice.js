import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Async thunks
export const fetchFeed = createAsyncThunk(
  'feed/fetchFeed',
  async ({ page = 1, type = 'all' }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/feed?page=${page}&type=${type}`);
      return { posts: response.data.posts, hasMore: response.data.hasMore, page };
    } catch (error) {
      // Fallback to mock data if API is not available
      if (error.code === 'ECONNREFUSED' || error.response?.status === 404) {
        console.warn('API not available, using mock feed data');

        // Generate mock posts
        const mockPosts = Array.from({ length: 5 }, (_, index) => ({
          _id: `mock-post-${page}-${index}`,
          content: `This is a sample post #${(page - 1) * 5 + index + 1}. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.`,
          type: index % 4 === 0 ? 'poll' : index % 3 === 0 ? 'image' : 'text',
          author: {
            _id: `mock-author-${index}`,
            username: `user${index + 1}`,
            realName: `User ${index + 1}`,
            avatar: `https://images.unsplash.com/photo-${1500000000000 + index}?w=100&h=100&fit=crop&crop=face`,
            isFollowed: Math.random() > 0.5
          },
          createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
          likesCount: Math.floor(Math.random() * 100),
          commentsCount: Math.floor(Math.random() * 20),
          sharesCount: Math.floor(Math.random() * 10),
          isLiked: Math.random() > 0.7,
          privacy: 'public',
          location: index % 2 === 0 ? 'New York, NY' : '',
          tags: index % 3 === 0 ? ['sample', 'mock', 'test'] : [],
          ...(index % 4 === 0 && {
            poll: {
              options: ['Option A', 'Option B', 'Option C'],
              votes: [
                { count: Math.floor(Math.random() * 50), voters: [] },
                { count: Math.floor(Math.random() * 50), voters: [] },
                { count: Math.floor(Math.random() * 50), voters: [] }
              ],
              totalVotes: Math.floor(Math.random() * 150),
              hasVoted: Math.random() > 0.5,
              duration: 24,
              allowMultipleChoices: false
            }
          }),
          ...(index % 3 === 0 && {
            media: [{
              type: 'image',
              url: `https://images.unsplash.com/photo-${1600000000000 + index}?w=600&h=400&fit=crop`,
              name: `image-${index}.jpg`
            }]
          })
        }));

        return {
          posts: mockPosts,
          hasMore: page < 3, // Mock pagination
          page
        };
      }

      return rejectWithValue(error.response?.data?.message || 'Failed to fetch feed');
    }
  }
);

export const createPost = createAsyncThunk(
  'feed/createPost',
  async (postData, { rejectWithValue }) => {
    try {
      // Use real API call for post creation
      const response = await axios.post(`${API_BASE_URL}/posts`, postData, {
        headers: {
          'Content-Type': postData instanceof FormData ? 'multipart/form-data' : 'application/json',
        },
      });
      return response.data;
    } catch (error) {
      // Fallback to mock data if API is not available
      if (error.code === 'ECONNREFUSED' || error.response?.status === 404) {
        console.warn('API not available, using mock data for post creation');

        // Create mock post as fallback
        const mockPost = {
          _id: Date.now().toString(),
          content: postData.content || postData.get?.('content') || '',
          type: postData.type || postData.get?.('type') || 'text',
          author: {
            _id: 'mock-user-id',
            username: 'mockuser',
            realName: 'Mock User',
            avatar: '/default-avatar.png',
            isFollowed: false
          },
          createdAt: new Date().toISOString(),
          likesCount: 0,
          commentsCount: 0,
          sharesCount: 0,
          isLiked: false,
          privacy: postData.privacy || postData.get?.('privacy') || 'public',
          location: postData.location || postData.get?.('location') || '',
          tags: postData.tags || (postData.get?.('tags') ? JSON.parse(postData.get('tags')) : []),
        };

        // Handle different post types
        if (mockPost.type === 'poll' && (postData.poll || postData.get?.('poll'))) {
          const pollData = postData.poll || JSON.parse(postData.get('poll'));
          mockPost.poll = {
            ...pollData,
            votes: pollData.options.map(() => ({ count: 0, voters: [] })),
            totalVotes: 0,
            hasVoted: false
          };
        }

        if (mockPost.type === 'event' && (postData.event || postData.get?.('event'))) {
          const eventData = postData.event || JSON.parse(postData.get('event'));
          mockPost.event = {
            ...eventData,
            attendees: [],
            isAttending: false
          };
        }

        // Handle media files (mock URLs for fallback)
        if (postData instanceof FormData) {
          const mediaFiles = [];
          for (let [key, value] of postData.entries()) {
            if (key.startsWith('media_') && value instanceof File) {
              mediaFiles.push({
                type: value.type.startsWith('image/') ? 'image' : 'video',
                url: URL.createObjectURL(value),
                name: value.name
              });
            }
          }
          if (mediaFiles.length > 0) {
            mockPost.media = mediaFiles;
          }
        }

        // Handle scheduled posts
        if (postData.scheduledFor || postData.get?.('scheduledFor')) {
          mockPost.scheduledFor = postData.scheduledFor || postData.get('scheduledFor');
          mockPost.status = 'scheduled';
        }

        return mockPost;
      }

      return rejectWithValue(error.response?.data?.message || 'Failed to create post');
    }
  }
);

export const likePost = createAsyncThunk(
  'feed/likePost',
  async (postId, { rejectWithValue, getState }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/posts/${postId}/like`);
      return { postId, liked: response.data.liked, likesCount: response.data.likesCount };
    } catch (error) {
      // Fallback to mock behavior if API is not available
      if (error.code === 'ECONNREFUSED' || error.response?.status === 404) {
        console.warn('API not available, using mock like behavior');

        const { feed } = getState();
        const post = feed.posts.find(p => p._id === postId);
        const currentlyLiked = post?.isLiked || false;

        return {
          postId,
          liked: !currentlyLiked,
          likesCount: (post?.likesCount || 0) + (currentlyLiked ? -1 : 1)
        };
      }

      return rejectWithValue(error.response?.data?.message || 'Failed to like post');
    }
  }
);

export const sharePost = createAsyncThunk(
  'feed/sharePost',
  async (postId, { rejectWithValue, getState }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/posts/${postId}/share`);
      return { postId, sharesCount: response.data.sharesCount };
    } catch (error) {
      // Fallback to mock behavior if API is not available
      if (error.code === 'ECONNREFUSED' || error.response?.status === 404) {
        console.warn('API not available, using mock share behavior');

        const { feed } = getState();
        const post = feed.posts.find(p => p._id === postId);

        return {
          postId,
          sharesCount: (post?.sharesCount || 0) + 1
        };
      }

      return rejectWithValue(error.response?.data?.message || 'Failed to share post');
    }
  }
);

export const fetchComments = createAsyncThunk(
  'feed/fetchComments',
  async ({ postId, page = 1 }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/posts/${postId}/comments?page=${page}`);
      return { postId, comments: response.data.comments, hasMore: response.data.hasMore };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch comments');
    }
  }
);

export const addComment = createAsyncThunk(
  'feed/addComment',
  async ({ postId, content }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/posts/${postId}/comments`, { content });
      return { postId, comment: response.data };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add comment');
    }
  }
);

export const fetchCommunityPosts = createAsyncThunk(
  'feed/fetchCommunityPosts',
  async ({ communityId, page = 1 }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/communities/${communityId}/posts?page=${page}`);
      return { posts: response.data.posts, hasMore: response.data.hasMore, page };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch community posts');
    }
  }
);

const initialState = {
  posts: [],
  isLoading: false,
  isLoadingMore: false,
  hasMore: true,
  currentPage: 1,
  feedType: 'all',
  error: null,
  comments: {},
  trending: [],
};

const feedSlice = createSlice({
  name: 'feed',
  initialState,
  reducers: {
    setFeedType: (state, action) => {
      state.feedType = action.payload;
      state.posts = [];
      state.currentPage = 1;
      state.hasMore = true;
    },
    updatePost: (state, action) => {
      const { postId, updates } = action.payload;
      const postIndex = state.posts.findIndex(p => p._id === postId);
      if (postIndex !== -1) {
        state.posts[postIndex] = { ...state.posts[postIndex], ...updates };
      }
    },
    removePost: (state, action) => {
      const postId = action.payload;
      state.posts = state.posts.filter(p => p._id !== postId);
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch feed
      .addCase(fetchFeed.pending, (state, action) => {
        if (action.meta.arg.page === 1) {
          state.isLoading = true;
        } else {
          state.isLoadingMore = true;
        }
        state.error = null;
      })
      .addCase(fetchFeed.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isLoadingMore = false;
        const { posts, hasMore, page } = action.payload;
        
        if (page === 1) {
          state.posts = posts;
        } else {
          state.posts = [...state.posts, ...posts];
        }
        
        state.hasMore = hasMore;
        state.currentPage = page;
      })
      .addCase(fetchFeed.rejected, (state, action) => {
        state.isLoading = false;
        state.isLoadingMore = false;
        state.error = action.payload;
      })
      // Create post
      .addCase(createPost.fulfilled, (state, action) => {
        state.posts.unshift(action.payload);
      })
      // Like post
      .addCase(likePost.fulfilled, (state, action) => {
        const { postId, liked, likesCount } = action.payload;
        const post = state.posts.find(p => p._id === postId);
        if (post) {
          post.liked = liked;
          post.likesCount = likesCount;
        }
      })
      // Share post
      .addCase(sharePost.fulfilled, (state, action) => {
        const { postId, sharesCount } = action.payload;
        const post = state.posts.find(p => p._id === postId);
        if (post) {
          post.sharesCount = sharesCount;
        }
      })
      // Fetch comments
      .addCase(fetchComments.fulfilled, (state, action) => {
        const { postId, comments } = action.payload;
        state.comments[postId] = comments;
      })
      // Add comment
      .addCase(addComment.fulfilled, (state, action) => {
        const { postId, comment } = action.payload;
        if (!state.comments[postId]) {
          state.comments[postId] = [];
        }
        state.comments[postId].unshift(comment);
        
        // Update comments count in post
        const post = state.posts.find(p => p._id === postId);
        if (post) {
          post.commentsCount = (post.commentsCount || 0) + 1;
        }
      })
      // Fetch community posts
      .addCase(fetchCommunityPosts.pending, (state, action) => {
        if (action.meta.arg.page === 1) {
          state.isLoading = true;
        } else {
          state.isLoadingMore = true;
        }
        state.error = null;
      })
      .addCase(fetchCommunityPosts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isLoadingMore = false;
        const { posts, hasMore, page } = action.payload;
        if (page === 1) {
          state.posts = posts;
        } else {
          state.posts = [...state.posts, ...posts];
        }
        state.hasMore = hasMore;
        state.currentPage = page;
      })
      .addCase(fetchCommunityPosts.rejected, (state, action) => {
        state.isLoading = false;
        state.isLoadingMore = false;
        state.error = action.payload;
      });
  },
});

export const {
  setFeedType,
  updatePost,
  removePost,
  clearError,
} = feedSlice.actions;

export default feedSlice.reducer;
