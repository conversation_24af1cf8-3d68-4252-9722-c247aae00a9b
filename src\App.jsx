import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import { useAuthInit } from './hooks/useAuthInit';
// Pages
import LandingPage from './pages/LandingPage';
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';
import OTPVerificationPage from './pages/auth/OTPVerificationPage';
import ForgotPasswordPage from './pages/auth/ForgotPasswordPage';
import ResetPasswordPage from './pages/auth/ResetPasswordPage';
import FeedPage from './pages/FeedPage';
import MessagesPage from './pages/MessagesPage';
import CommunitiesPage from './pages/CommunitiesPage';
import ExplorePage from './pages/ExplorePage';
import ProfilePage from './pages/ProfilePage';
import SettingsPage from './pages/SettingsPage';
import FollowersPage from './pages/FollowersPage';
import FollowingPage from './pages/FollowingPage';
import SearchPage from './pages/SearchPage';
import NotificationsPage from './pages/NotificationsPage';
import DraftPostsPage from './pages/DraftPostsPage';
import SchedulePostPage from './pages/SchedulePostPage';
// Components
import ProtectedRoute from './components/navigation/ProtectedRoute';
import Layout from './components/layouts/Layout';
import ErrorBoundary from './components/error/ErrorBoundary';

const App = () => {
  // Initialize authentication state on app startup
  const { isAuthenticated, isLoading } = useAuthInit();

  // Show loading screen while initializing authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Initializing...</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <Router>
        <AnimatePresence mode="wait">
          <Routes>
          {/* Public Routes */}
          <Route path="/" element={<LandingPage />} />
          <Route
            path="/login"
            element={!isAuthenticated ? <LoginPage /> : <Navigate to="/feed" />}
          />
          <Route
            path="/register"
            element={!isAuthenticated ? <RegisterPage /> : <Navigate to="/feed" />}
          />
          <Route
            path="/verify-otp"
            element={!isAuthenticated ? <OTPVerificationPage /> : <Navigate to="/feed" />}
          />
          <Route
            path="/forgot-password"
            element={!isAuthenticated ? <ForgotPasswordPage /> : <Navigate to="/feed" />}
          />
          <Route
            path="/reset-password"
            element={!isAuthenticated ? <ResetPasswordPage /> : <Navigate to="/feed" />}
          />
          
          {/* Explore Route - Accessible to both authenticated and unauthenticated users */}
          <Route path="/explore" element={<ExplorePage />} />
          
          {/* Protected Routes */}
          <Route element={<ProtectedRoute />}>
            <Route element={<Layout />}>
              <Route path="/feed" element={<FeedPage />} />
              <Route path="/search" element={<SearchPage />} />
              <Route path="/notifications" element={<NotificationsPage />} />
              <Route path="/messages" element={<MessagesPage />} />
              <Route path="/communities" element={<CommunitiesPage />} />
              <Route path="/profile/:username" element={<ProfilePage />} />
              <Route path="/profile/:userId/followers" element={<FollowersPage />} />
              <Route path="/profile/:userId/following" element={<FollowingPage />} />
              <Route path="/settings" element={<SettingsPage />} />
              <Route path="/drafts" element={<DraftPostsPage />} />
              <Route path="/schedule" element={<SchedulePostPage />} />
            </Route>
          </Route>

          {/* Catch-all Route */}
          <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </AnimatePresence>
      </Router>
    </ErrorBoundary>
  );
};

export default App;