import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from '../../__tests__/utils/testUtils';
import SchedulePostPage from '../SchedulePostPage';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock navigate and search params
const mockNavigate = jest.fn();
const mockSearchParams = new URLSearchParams();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useSearchParams: () => [mockSearchParams, jest.fn()],
}));

// Mock window.alert
global.alert = jest.fn();

const mockScheduledPosts = [
  {
    id: '1',
    title: 'Weekly Tech Update',
    content: 'Here are the latest tech updates for this week...',
    scheduledAt: '2024-01-20T09:00:00Z',
    createdAt: '2024-01-15T10:30:00Z',
    status: 'scheduled',
    community: 'tech-enthusiasts',
    tags: ['tech', 'weekly', 'update'],
    wordCount: 245,
  },
  {
    id: '2',
    title: 'Design System Launch',
    content: 'Excited to announce the launch of our new design system...',
    scheduledAt: '2024-01-22T14:30:00Z',
    createdAt: '2024-01-16T11:15:00Z',
    status: 'scheduled',
    community: 'design-community',
    tags: ['design', 'system', 'launch'],
    wordCount: 189,
  },
];

describe('SchedulePostPage', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    mockNavigate.mockClear();
    global.alert.mockClear();
    // Mock current date to ensure consistent testing
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-01-15T12:00:00Z'));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders page header correctly', () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockScheduledPosts));
    renderWithProviders(<SchedulePostPage />);

    expect(screen.getByText('Scheduled Posts')).toBeInTheDocument();
    expect(screen.getByText('2 scheduled posts')).toBeInTheDocument();
  });

  it('shows singular post count', () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify([mockScheduledPosts[0]]));
    renderWithProviders(<SchedulePostPage />);

    expect(screen.getByText('1 scheduled post')).toBeInTheDocument();
  });

  it('renders schedule post button', () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockScheduledPosts));
    renderWithProviders(<SchedulePostPage />);

    expect(screen.getByText('Schedule Post')).toBeInTheDocument();
  });

  it('shows loading state initially', () => {
    localStorageMock.getItem.mockReturnValue(null);
    renderWithProviders(<SchedulePostPage />);

    const loadingElements = document.querySelectorAll('.animate-pulse');
    expect(loadingElements.length).toBeGreaterThan(0);
  });

  it('shows empty state when no scheduled posts', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify([]));
    renderWithProviders(<SchedulePostPage />);

    await waitFor(() => {
      expect(screen.getByText('No scheduled posts')).toBeInTheDocument();
      expect(screen.getByText('Schedule your first post to see it here.')).toBeInTheDocument();
    });
  });

  it('renders scheduled posts list', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockScheduledPosts));
    renderWithProviders(<SchedulePostPage />);

    await waitFor(() => {
      expect(screen.getByText('Weekly Tech Update')).toBeInTheDocument();
      expect(screen.getByText('Design System Launch')).toBeInTheDocument();
    });
  });

  it('displays post metadata', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockScheduledPosts));
    renderWithProviders(<SchedulePostPage />);

    await waitFor(() => {
      expect(screen.getByText('245 words')).toBeInTheDocument();
      expect(screen.getByText('189 words')).toBeInTheDocument();
      expect(screen.getByText('tech-enthusiasts')).toBeInTheDocument();
      expect(screen.getByText('design-community')).toBeInTheDocument();
    });
  });

  it('opens schedule form when button clicked', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify([]));
    renderWithProviders(<SchedulePostPage />);

    const scheduleButton = screen.getByText('Schedule Post');
    await user.click(scheduleButton);

    expect(screen.getByText('Schedule Post')).toBeInTheDocument();
    expect(screen.getByLabelText('Title')).toBeInTheDocument();
    expect(screen.getByLabelText('Content')).toBeInTheDocument();
  });

  it('validates form submission', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify([]));
    renderWithProviders(<SchedulePostPage />);

    // Open form
    const scheduleButton = screen.getByText('Schedule Post');
    await user.click(scheduleButton);

    // Try to submit without required fields
    const submitButton = screen.getByRole('button', { name: /schedule post/i });
    await user.click(submitButton);

    // Should show validation message (mocked alert)
    expect(global.alert).toHaveBeenCalledWith('Please select both date and time');
  });

  it('prevents scheduling in the past', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify([]));
    renderWithProviders(<SchedulePostPage />);

    // Open form
    const scheduleButton = screen.getByText('Schedule Post');
    await user.click(scheduleButton);

    // Fill form with past date
    await user.type(screen.getByLabelText('Title'), 'Test Post');
    await user.type(screen.getByLabelText('Content'), 'Test content');
    await user.type(screen.getByLabelText('Date'), '2024-01-01');
    await user.type(screen.getByLabelText('Time'), '10:00');

    const submitButton = screen.getByRole('button', { name: /schedule post/i });
    await user.click(submitButton);

    expect(global.alert).toHaveBeenCalledWith('Please select a future date and time');
  });

  it('successfully schedules a post', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify([]));
    renderWithProviders(<SchedulePostPage />);

    // Open form
    const scheduleButton = screen.getByText('Schedule Post');
    await user.click(scheduleButton);

    // Fill form with valid future date
    await user.type(screen.getByLabelText('Title'), 'Test Post');
    await user.type(screen.getByLabelText('Content'), 'Test content');
    await user.type(screen.getByLabelText('Date'), '2024-01-20');
    await user.type(screen.getByLabelText('Time'), '15:00');

    const submitButton = screen.getByRole('button', { name: /schedule post/i });
    await user.click(submitButton);

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'scheduledPosts',
      expect.stringContaining('Test Post')
    );
    expect(global.alert).toHaveBeenCalledWith('Post scheduled successfully!');
  });

  it('handles post selection', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockScheduledPosts));
    renderWithProviders(<SchedulePostPage />);

    await waitFor(async () => {
      const checkboxes = screen.getAllByRole('checkbox');
      await user.click(checkboxes[0]); // Select first post

      expect(screen.getByText('Delete Selected (1)')).toBeInTheDocument();
    });
  });

  it('handles post deletion', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockScheduledPosts));
    renderWithProviders(<SchedulePostPage />);

    await waitFor(async () => {
      const deleteButtons = screen.getAllByTitle('Delete scheduled post');
      await user.click(deleteButtons[0]);

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'scheduledPosts',
        JSON.stringify([mockScheduledPosts[1]]) // Should remove first post
      );
    });
  });

  it('handles publish now', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockScheduledPosts));
    renderWithProviders(<SchedulePostPage />);

    await waitFor(async () => {
      const publishButtons = screen.getAllByText('Publish Now');
      await user.click(publishButtons[0]);

      expect(global.alert).toHaveBeenCalledWith('Post published successfully!');
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'scheduledPosts',
        JSON.stringify([mockScheduledPosts[1]]) // Should remove published post
      );
    });
  });

  it('handles bulk deletion', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockScheduledPosts));
    renderWithProviders(<SchedulePostPage />);

    await waitFor(async () => {
      // Select posts
      const checkboxes = screen.getAllByRole('checkbox');
      await user.click(checkboxes[0]);
      await user.click(checkboxes[1]);

      // Delete selected
      const deleteSelectedButton = screen.getByText('Delete Selected (2)');
      await user.click(deleteSelectedButton);

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'scheduledPosts',
        JSON.stringify([]) // Should remove all selected posts
      );
    });
  });

  it('formats scheduled time correctly', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockScheduledPosts));
    renderWithProviders(<SchedulePostPage />);

    await waitFor(() => {
      // Should show formatted dates and times
      expect(screen.getByText(/Jan/)).toBeInTheDocument();
      expect(screen.getByText(/AM|PM/)).toBeInTheDocument();
    });
  });

  it('shows time until scheduled', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockScheduledPosts));
    renderWithProviders(<SchedulePostPage />);

    await waitFor(() => {
      // Should show "in X days" or similar
      expect(screen.getByText(/in \d+d/)).toBeInTheDocument();
    });
  });

  it('sorts posts by scheduled time', async () => {
    const unsortedPosts = [mockScheduledPosts[1], mockScheduledPosts[0]]; // Reverse order
    localStorageMock.getItem.mockReturnValue(JSON.stringify(unsortedPosts));
    renderWithProviders(<SchedulePostPage />);

    await waitFor(() => {
      const titles = screen.getAllByRole('heading', { level: 3 });
      expect(titles[0]).toHaveTextContent('Weekly Tech Update'); // Earlier date should be first
      expect(titles[1]).toHaveTextContent('Design System Launch');
    });
  });

  it('handles form cancellation', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify([]));
    renderWithProviders(<SchedulePostPage />);

    // Open form
    const scheduleButton = screen.getByText('Schedule Post');
    await user.click(scheduleButton);

    // Cancel form
    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    // Form should be closed
    expect(screen.queryByLabelText('Title')).not.toBeInTheDocument();
  });

  it('sets minimum date to today', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify([]));
    renderWithProviders(<SchedulePostPage />);

    // Open form
    const scheduleButton = screen.getByText('Schedule Post');
    await user.click(scheduleButton);

    const dateInput = screen.getByLabelText('Date');
    expect(dateInput).toHaveAttribute('min', '2024-01-15'); // Current date
  });

  it('has proper responsive design', () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockScheduledPosts));
    const { container } = renderWithProviders(<SchedulePostPage />);

    const mainContainer = container.firstChild;
    expect(mainContainer).toHaveClass('max-w-4xl', 'mx-auto', 'p-4', 'pb-20');
  });

  it('loads mock data when localStorage is empty', async () => {
    localStorageMock.getItem.mockReturnValue(null);
    renderWithProviders(<SchedulePostPage />);

    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'scheduledPosts',
        expect.stringContaining('Weekly Tech Update')
      );
    });
  });

  it('handles community selection in form', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify([]));
    renderWithProviders(<SchedulePostPage />);

    // Open form
    const scheduleButton = screen.getByText('Schedule Post');
    await user.click(scheduleButton);

    const communitySelect = screen.getByLabelText('Community (Optional)');
    await user.selectOptions(communitySelect, 'tech-enthusiasts');

    expect(communitySelect).toHaveValue('tech-enthusiasts');
  });
});
