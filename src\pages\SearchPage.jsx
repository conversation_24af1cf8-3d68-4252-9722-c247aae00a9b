import { useState, useEffect, useCallback } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  UserIcon,
  HashtagIcon,
  DocumentTextIcon,
  UserGroupIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

const SearchPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [query, setQuery] = useState(searchParams.get('q') || '');
  const [activeTab, setActiveTab] = useState('all');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState({
    users: [],
    posts: [],
    communities: [],
    hashtags: [],
  });
  const [recentSearches, setRecentSearches] = useState([]);

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  // Save recent searches to localStorage
  const saveRecentSearch = useCallback((searchQuery) => {
    if (!searchQuery.trim()) return;
    
    const updated = [
      searchQuery,
      ...recentSearches.filter(s => s !== searchQuery)
    ].slice(0, 10); // Keep only 10 recent searches
    
    setRecentSearches(updated);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
  }, [recentSearches]);

  // Mock search function (replace with actual API call)
  const performSearch = useCallback(async (searchQuery) => {
    if (!searchQuery.trim()) {
      setResults({ users: [], posts: [], communities: [], hashtags: [] });
      return;
    }

    setIsLoading(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock results - replace with actual API calls
    const mockResults = {
      users: [
        {
          id: 1,
          username: 'john_doe',
          name: 'John Doe',
          avatar: null,
          bio: 'Software developer and tech enthusiast',
          followers: 1234,
          isFollowing: false,
        },
        {
          id: 2,
          username: 'jane_smith',
          name: 'Jane Smith',
          avatar: null,
          bio: 'Designer & Creative Director',
          followers: 856,
          isFollowing: true,
        },
      ],
      posts: [
        {
          id: 1,
          content: `This is a sample post that matches the search query "${searchQuery}"`,
          author: { username: 'john_doe', name: 'John Doe' },
          createdAt: '2024-01-15T10:30:00Z',
          likes: 42,
          comments: 8,
        },
        {
          id: 2,
          content: `Another post containing "${searchQuery}" in the content`,
          author: { username: 'jane_smith', name: 'Jane Smith' },
          createdAt: '2024-01-14T15:45:00Z',
          likes: 28,
          comments: 5,
        },
      ],
      communities: [
        {
          id: 1,
          name: 'Tech Enthusiasts',
          slug: 'tech-enthusiasts',
          description: 'A community for technology lovers',
          members: 5420,
          isJoined: false,
        },
        {
          id: 2,
          name: 'Design Community',
          slug: 'design-community',
          description: 'Share and discuss design trends',
          members: 3210,
          isJoined: true,
        },
      ],
      hashtags: [
        { tag: 'technology', count: 1250 },
        { tag: 'design', count: 890 },
        { tag: 'programming', count: 756 },
      ],
    };

    setResults(mockResults);
    setIsLoading(false);
  }, []);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    if (query.trim()) {
      setSearchParams({ q: query });
      saveRecentSearch(query);
      performSearch(query);
    }
  };

  // Handle input change
  const handleInputChange = (e) => {
    setQuery(e.target.value);
  };

  // Clear search
  const clearSearch = () => {
    setQuery('');
    setSearchParams({});
    setResults({ users: [], posts: [], communities: [], hashtags: [] });
  };

  // Remove recent search
  const removeRecentSearch = (searchToRemove) => {
    const updated = recentSearches.filter(s => s !== searchToRemove);
    setRecentSearches(updated);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
  };

  // Search from recent searches
  const searchFromRecent = (recentQuery) => {
    setQuery(recentQuery);
    setSearchParams({ q: recentQuery });
    performSearch(recentQuery);
  };

  // Perform search on mount if query exists
  useEffect(() => {
    const urlQuery = searchParams.get('q');
    if (urlQuery) {
      setQuery(urlQuery);
      performSearch(urlQuery);
    }
  }, [searchParams, performSearch]);

  const tabs = [
    { key: 'all', label: 'All', icon: MagnifyingGlassIcon },
    { key: 'users', label: 'People', icon: UserIcon },
    { key: 'posts', label: 'Posts', icon: DocumentTextIcon },
    { key: 'communities', label: 'Communities', icon: UserGroupIcon },
    { key: 'hashtags', label: 'Hashtags', icon: HashtagIcon },
  ];

  const getFilteredResults = () => {
    if (activeTab === 'all') return results;
    return { [activeTab]: results[activeTab] };
  };

  const hasResults = Object.values(results).some(arr => arr.length > 0);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="max-w-2xl mx-auto p-4 pb-20"
    >
      {/* Search Header */}
      <div className="mb-6">
        <form onSubmit={handleSearch} className="relative">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search for people, posts, communities..."
              value={query}
              onChange={handleInputChange}
              className="w-full pl-10 pr-10 py-3 bg-gray-100 dark:bg-dark-700 border-0 rounded-full text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
              autoFocus
            />
            {query && (
              <button
                type="button"
                onClick={clearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            )}
          </div>
        </form>
      </div>

      {/* Recent Searches */}
      {!query && recentSearches.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Recent Searches
          </h3>
          <div className="space-y-2">
            {recentSearches.map((recentQuery, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-600 transition-colors"
              >
                <button
                  onClick={() => searchFromRecent(recentQuery)}
                  className="flex items-center space-x-3 flex-1 text-left"
                >
                  <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-900 dark:text-white">{recentQuery}</span>
                </button>
                <button
                  onClick={() => removeRecentSearch(recentQuery)}
                  className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Search Results */}
      {query && (
        <>
          {/* Filter Tabs */}
          <div className="flex space-x-1 mb-6 bg-gray-100 dark:bg-dark-700 rounded-lg p-1 overflow-x-auto">
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className={`flex items-center space-x-2 py-2 px-4 text-sm font-medium rounded-md transition-colors whitespace-nowrap ${
                    activeTab === tab.key
                      ? 'bg-white dark:bg-dark-800 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <IconComponent className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="space-y-4">
              {[...Array(3)].map((_, index) => (
                <div
                  key={index}
                  className="bg-white dark:bg-dark-800 rounded-lg p-4 animate-pulse"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-200 dark:bg-dark-700 rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 dark:bg-dark-700 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 dark:bg-dark-700 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* No Results */}
          {!isLoading && !hasResults && (
            <div className="text-center py-12">
              <MagnifyingGlassIcon className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No results found
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                Try searching for something else or check your spelling.
              </p>
            </div>
          )}

          {/* Results */}
          {!isLoading && hasResults && (
            <div className="space-y-6">
              {/* Users */}
              {(activeTab === 'all' || activeTab === 'users') && results.users.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    People
                  </h3>
                  <div className="space-y-3">
                    {results.users.map((user) => (
                      <div
                        key={user.id}
                        className="bg-white dark:bg-dark-800 rounded-lg p-4 border border-gray-200 dark:border-dark-700"
                      >
                        <div className="flex items-center justify-between">
                          <Link
                            to={`/profile/${user.username}`}
                            className="flex items-center space-x-3 flex-1"
                          >
                            {user.avatar ? (
                              <img
                                src={user.avatar}
                                alt={user.name}
                                className="w-10 h-10 rounded-full"
                              />
                            ) : (
                              <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                <span className="text-primary-600 dark:text-primary-400 font-semibold">
                                  {user.name.charAt(0).toUpperCase()}
                                </span>
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <p className="font-semibold text-gray-900 dark:text-white">
                                {user.name}
                              </p>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                @{user.username}
                              </p>
                              {user.bio && (
                                <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                  {user.bio}
                                </p>
                              )}
                              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                {user.followers} followers
                              </p>
                            </div>
                          </Link>
                          <button
                            className={`px-4 py-2 text-sm font-medium rounded-full transition-colors ${
                              user.isFollowing
                                ? 'bg-gray-200 dark:bg-dark-600 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-dark-500'
                                : 'bg-primary-600 text-white hover:bg-primary-700'
                            }`}
                          >
                            {user.isFollowing ? 'Following' : 'Follow'}
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Posts */}
              {(activeTab === 'all' || activeTab === 'posts') && results.posts.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    Posts
                  </h3>
                  <div className="space-y-3">
                    {results.posts.map((post) => (
                      <div
                        key={post.id}
                        className="bg-white dark:bg-dark-800 rounded-lg p-4 border border-gray-200 dark:border-dark-700"
                      >
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                            <span className="text-primary-600 dark:text-primary-400 font-semibold text-sm">
                              {post.author.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-2">
                              <Link
                                to={`/profile/${post.author.username}`}
                                className="font-semibold text-gray-900 dark:text-white hover:underline"
                              >
                                {post.author.name}
                              </Link>
                              <span className="text-gray-500 dark:text-gray-400">
                                @{post.author.username}
                              </span>
                              <span className="text-gray-500 dark:text-gray-400">·</span>
                              <span className="text-gray-500 dark:text-gray-400 text-sm">
                                {new Date(post.createdAt).toLocaleDateString()}
                              </span>
                            </div>
                            <p className="text-gray-900 dark:text-white mb-3">
                              {post.content}
                            </p>
                            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                              <span>{post.likes} likes</span>
                              <span>{post.comments} comments</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Communities */}
              {(activeTab === 'all' || activeTab === 'communities') && results.communities.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    Communities
                  </h3>
                  <div className="space-y-3">
                    {results.communities.map((community) => (
                      <div
                        key={community.id}
                        className="bg-white dark:bg-dark-800 rounded-lg p-4 border border-gray-200 dark:border-dark-700"
                      >
                        <div className="flex items-center justify-between">
                          <Link
                            to={`/communities/${community.slug}`}
                            className="flex-1"
                          >
                            <h4 className="font-semibold text-gray-900 dark:text-white">
                              {community.name}
                            </h4>
                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                              {community.description}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                              {community.members} members
                            </p>
                          </Link>
                          <button
                            className={`px-4 py-2 text-sm font-medium rounded-full transition-colors ${
                              community.isJoined
                                ? 'bg-gray-200 dark:bg-dark-600 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-dark-500'
                                : 'bg-primary-600 text-white hover:bg-primary-700'
                            }`}
                          >
                            {community.isJoined ? 'Joined' : 'Join'}
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Hashtags */}
              {(activeTab === 'all' || activeTab === 'hashtags') && results.hashtags.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    Hashtags
                  </h3>
                  <div className="space-y-2">
                    {results.hashtags.map((hashtag) => (
                      <Link
                        key={hashtag.tag}
                        to={`/explore?hashtag=${hashtag.tag}`}
                        className="block bg-white dark:bg-dark-800 rounded-lg p-4 border border-gray-200 dark:border-dark-700 hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-semibold text-gray-900 dark:text-white">
                              #{hashtag.tag}
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {hashtag.count} posts
                            </p>
                          </div>
                          <HashtagIcon className="h-5 w-5 text-gray-400" />
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </>
      )}
    </motion.div>
  );
};

export default SearchPage;
