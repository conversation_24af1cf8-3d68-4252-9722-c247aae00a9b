import React from 'react';
import { motion } from 'framer-motion';
import { 
  ExclamationTriangleIcon, 
  ArrowPathIcon,
  HomeIcon,
  BugAntIcon 
} from '@heroicons/react/24/outline';
import { Button } from '../ui/Button';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { 
      hasError: true,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Report error to monitoring service (if available)
    if (window.gtag) {
      window.gtag('event', 'exception', {
        description: error.toString(),
        fatal: false
      });
    }

    // Report to custom error tracking
    this.reportError(error, errorInfo);
  }

  reportError = (error, errorInfo) => {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errorId: this.state.errorId
    };

    // In a real app, send this to your error tracking service
    console.log('Error Report:', errorReport);
    
    // Store in localStorage for debugging
    try {
      const existingErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
      existingErrors.push(errorReport);
      // Keep only last 10 errors
      if (existingErrors.length > 10) {
        existingErrors.shift();
      }
      localStorage.setItem('app_errors', JSON.stringify(existingErrors));
    } catch (e) {
      console.error('Failed to store error report:', e);
    }
  };

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null 
    });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleReportBug = () => {
    const errorDetails = {
      message: this.state.error?.message || 'Unknown error',
      stack: this.state.error?.stack || 'No stack trace',
      errorId: this.state.errorId,
      timestamp: new Date().toISOString()
    };

    const subject = encodeURIComponent(`Bug Report - Error ID: ${this.state.errorId}`);
    const body = encodeURIComponent(`
Error Details:
- Message: ${errorDetails.message}
- Error ID: ${errorDetails.errorId}
- Timestamp: ${errorDetails.timestamp}
- URL: ${window.location.href}
- User Agent: ${navigator.userAgent}

Stack Trace:
${errorDetails.stack}

Please describe what you were doing when this error occurred:
[Your description here]
    `);

    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo, errorId } = this.state;
      const isDevelopment = import.meta.env.DEV;

      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="max-w-2xl w-full bg-gray-900 rounded-lg border border-gray-800 p-8 text-center"
          >
            {/* Error Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2 }}
              className="mx-auto w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-6"
            >
              <ExclamationTriangleIcon className="h-8 w-8 text-red-400" />
            </motion.div>

            {/* Error Message */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <h1 className="text-2xl font-bold text-white mb-2">
                Oops! Something went wrong
              </h1>
              <p className="text-gray-400 mb-6">
                We're sorry for the inconvenience. An unexpected error has occurred.
              </p>

              {/* Error ID */}
              <div className="bg-gray-800 rounded-lg p-3 mb-6">
                <p className="text-sm text-gray-400 mb-1">Error ID</p>
                <p className="text-white font-mono text-sm">{errorId}</p>
              </div>

              {/* Development Error Details */}
              {isDevelopment && error && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="bg-red-900/20 border border-red-800 rounded-lg p-4 mb-6 text-left"
                >
                  <h3 className="text-red-400 font-semibold mb-2">Development Error Details:</h3>
                  <div className="text-sm text-red-300 mb-2">
                    <strong>Message:</strong> {error.message}
                  </div>
                  {error.stack && (
                    <details className="text-xs text-red-300">
                      <summary className="cursor-pointer hover:text-red-200 mb-2">
                        Stack Trace
                      </summary>
                      <pre className="whitespace-pre-wrap bg-red-900/30 p-2 rounded overflow-x-auto">
                        {error.stack}
                      </pre>
                    </details>
                  )}
                  {errorInfo?.componentStack && (
                    <details className="text-xs text-red-300 mt-2">
                      <summary className="cursor-pointer hover:text-red-200 mb-2">
                        Component Stack
                      </summary>
                      <pre className="whitespace-pre-wrap bg-red-900/30 p-2 rounded overflow-x-auto">
                        {errorInfo.componentStack}
                      </pre>
                    </details>
                  )}
                </motion.div>
              )}

              {/* Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="flex flex-col sm:flex-row gap-3 justify-center"
              >
                <Button
                  onClick={this.handleRetry}
                  className="flex items-center justify-center space-x-2"
                >
                  <ArrowPathIcon className="h-4 w-4" />
                  <span>Try Again</span>
                </Button>

                <Button
                  onClick={this.handleGoHome}
                  variant="secondary"
                  className="flex items-center justify-center space-x-2"
                >
                  <HomeIcon className="h-4 w-4" />
                  <span>Go Home</span>
                </Button>

                <Button
                  onClick={this.handleReportBug}
                  variant="outline"
                  className="flex items-center justify-center space-x-2"
                >
                  <BugAntIcon className="h-4 w-4" />
                  <span>Report Bug</span>
                </Button>
              </motion.div>

              {/* Help Text */}
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="text-sm text-gray-500 mt-6"
              >
                If this problem persists, please contact our support team with the error ID above.
              </motion.p>
            </motion.div>
          </motion.div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for functional components
export const withErrorBoundary = (Component, fallback) => {
  return function WithErrorBoundaryComponent(props) {
    return (
      <ErrorBoundary fallback={fallback}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
};

// Hook for error reporting in functional components
export const useErrorHandler = () => {
  const reportError = (error, errorInfo = {}) => {
    console.error('Manual error report:', error, errorInfo);
    
    const errorReport = {
      message: error.message || error.toString(),
      stack: error.stack || 'No stack trace available',
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...errorInfo
    };

    // Store in localStorage for debugging
    try {
      const existingErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
      existingErrors.push(errorReport);
      if (existingErrors.length > 10) {
        existingErrors.shift();
      }
      localStorage.setItem('app_errors', JSON.stringify(existingErrors));
    } catch (e) {
      console.error('Failed to store error report:', e);
    }

    // Report to monitoring service if available
    if (window.gtag) {
      window.gtag('event', 'exception', {
        description: error.toString(),
        fatal: false
      });
    }
  };

  return { reportError };
};

export default ErrorBoundary;
