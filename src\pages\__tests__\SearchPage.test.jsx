import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, mockSearchResults } from '../../__tests__/utils/testUtils';
import SearchPage from '../SearchPage';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock URLSearchParams
const mockSetSearchParams = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useSearchParams: () => [
    new URLSearchParams(),
    mockSetSearchParams,
  ],
}));

describe('SearchPage', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
    mockSetSearchParams.mockClear();
  });

  it('renders search input', () => {
    renderWithProviders(<SearchPage />);

    const searchInput = screen.getByPlaceholderText('Search for people, posts, communities...');
    expect(searchInput).toBeInTheDocument();
  });

  it('renders filter tabs', () => {
    renderWithProviders(<SearchPage />);

    expect(screen.getByText('All')).toBeInTheDocument();
    expect(screen.getByText('People')).toBeInTheDocument();
    expect(screen.getByText('Posts')).toBeInTheDocument();
    expect(screen.getByText('Communities')).toBeInTheDocument();
    expect(screen.getByText('Hashtags')).toBeInTheDocument();
  });

  it('shows recent searches when no query', () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(['react', 'javascript']));

    renderWithProviders(<SearchPage />);

    expect(screen.getByText('Recent Searches')).toBeInTheDocument();
    expect(screen.getByText('react')).toBeInTheDocument();
    expect(screen.getByText('javascript')).toBeInTheDocument();
  });

  it('handles search input change', async () => {
    const user = userEvent.setup();
    renderWithProviders(<SearchPage />);

    const searchInput = screen.getByPlaceholderText('Search for people, posts, communities...');
    await user.type(searchInput, 'test query');

    expect(searchInput).toHaveValue('test query');
  });

  it('performs search on form submission', async () => {
    const user = userEvent.setup();
    renderWithProviders(<SearchPage />);

    const searchInput = screen.getByPlaceholderText('Search for people, posts, communities...');
    await user.type(searchInput, 'test query');

    const form = searchInput.closest('form');
    fireEvent.submit(form);

    expect(mockSetSearchParams).toHaveBeenCalledWith({ q: 'test query' });
  });

  it('clears search when clear button is clicked', async () => {
    const user = userEvent.setup();
    renderWithProviders(<SearchPage />);

    const searchInput = screen.getByPlaceholderText('Search for people, posts, communities...');
    await user.type(searchInput, 'test query');

    const clearButton = screen.getByRole('button', { name: /clear/i });
    await user.click(clearButton);

    expect(searchInput).toHaveValue('');
    expect(mockSetSearchParams).toHaveBeenCalledWith({});
  });

  it('saves search to recent searches', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify([]));

    renderWithProviders(<SearchPage />);

    const searchInput = screen.getByPlaceholderText('Search for people, posts, communities...');
    await user.type(searchInput, 'test query');

    const form = searchInput.closest('form');
    fireEvent.submit(form);

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'recentSearches',
      JSON.stringify(['test query'])
    );
  });

  it('removes recent search when X is clicked', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(['react', 'javascript']));

    renderWithProviders(<SearchPage />);

    const removeButtons = screen.getAllByRole('button', { name: /remove/i });
    await user.click(removeButtons[0]);

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'recentSearches',
      JSON.stringify(['javascript'])
    );
  });

  it('searches from recent search when clicked', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(['react']));

    renderWithProviders(<SearchPage />);

    const recentSearchButton = screen.getByText('react');
    await user.click(recentSearchButton);

    expect(mockSetSearchParams).toHaveBeenCalledWith({ q: 'react' });
  });

  it('shows loading state during search', async () => {
    renderWithProviders(<SearchPage />);

    // Simulate search with loading
    const searchInput = screen.getByPlaceholderText('Search for people, posts, communities...');
    await userEvent.type(searchInput, 'test');

    const form = searchInput.closest('form');
    fireEvent.submit(form);

    // Should show loading skeletons
    await waitFor(() => {
      const loadingElements = document.querySelectorAll('.animate-pulse');
      expect(loadingElements.length).toBeGreaterThan(0);
    });
  });

  it('shows no results message when search returns empty', async () => {
    renderWithProviders(<SearchPage />);

    const searchInput = screen.getByPlaceholderText('Search for people, posts, communities...');
    await userEvent.type(searchInput, 'nonexistent');

    const form = searchInput.closest('form');
    fireEvent.submit(form);

    await waitFor(() => {
      expect(screen.getByText('No results found')).toBeInTheDocument();
      expect(screen.getByText('Try searching for something else or check your spelling.')).toBeInTheDocument();
    });
  });

  it('filters results by tab selection', async () => {
    const user = userEvent.setup();
    renderWithProviders(<SearchPage />);

    // Perform a search first
    const searchInput = screen.getByPlaceholderText('Search for people, posts, communities...');
    await user.type(searchInput, 'test');

    const form = searchInput.closest('form');
    fireEvent.submit(form);

    // Wait for results and click People tab
    await waitFor(() => {
      const peopleTab = screen.getByText('People');
      return user.click(peopleTab);
    });

    // Should filter to only show people results
    expect(screen.getByText('People')).toHaveClass('bg-white');
  });

  it('displays user search results correctly', async () => {
    // Mock the search to return results
    const searchPageWithResults = () => {
      const [query, setQuery] = React.useState('test');
      const [results, setResults] = React.useState(mockSearchResults);
      const [isLoading, setIsLoading] = React.useState(false);

      return (
        <div>
          {results.users.map(user => (
            <div key={user.id}>
              <span>{user.name}</span>
              <span>@{user.username}</span>
              <span>{user.bio}</span>
              <span>{user.followers} followers</span>
            </div>
          ))}
        </div>
      );
    };

    renderWithProviders(searchPageWithResults());

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('@johndoe')).toBeInTheDocument();
    expect(screen.getByText('Software developer')).toBeInTheDocument();
    expect(screen.getByText('150 followers')).toBeInTheDocument();
  });

  it('displays post search results correctly', async () => {
    const searchPageWithResults = () => {
      const [results, setResults] = React.useState(mockSearchResults);

      return (
        <div>
          {results.posts.map(post => (
            <div key={post.id}>
              <span>{post.content}</span>
              <span>{post.author.name}</span>
              <span>{post.likes} likes</span>
              <span>{post.comments} comments</span>
            </div>
          ))}
        </div>
      );
    };

    renderWithProviders(searchPageWithResults());

    expect(screen.getByText('This is a test post')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('10 likes')).toBeInTheDocument();
    expect(screen.getByText('5 comments')).toBeInTheDocument();
  });

  it('displays community search results correctly', async () => {
    const searchPageWithResults = () => {
      const [results, setResults] = React.useState(mockSearchResults);

      return (
        <div>
          {results.communities.map(community => (
            <div key={community.id}>
              <span>{community.name}</span>
              <span>{community.description}</span>
              <span>{community.members} members</span>
            </div>
          ))}
        </div>
      );
    };

    renderWithProviders(searchPageWithResults());

    expect(screen.getByText('Tech Community')).toBeInTheDocument();
    expect(screen.getByText('A community for tech enthusiasts')).toBeInTheDocument();
    expect(screen.getByText('1000 members')).toBeInTheDocument();
  });

  it('displays hashtag search results correctly', async () => {
    const searchPageWithResults = () => {
      const [results, setResults] = React.useState(mockSearchResults);

      return (
        <div>
          {results.hashtags.map(hashtag => (
            <div key={hashtag.tag}>
              <span>#{hashtag.tag}</span>
              <span>{hashtag.count} posts</span>
            </div>
          ))}
        </div>
      );
    };

    renderWithProviders(searchPageWithResults());

    expect(screen.getByText('#technology')).toBeInTheDocument();
    expect(screen.getByText('500 posts')).toBeInTheDocument();
    expect(screen.getByText('#programming')).toBeInTheDocument();
    expect(screen.getByText('300 posts')).toBeInTheDocument();
  });

  it('has proper responsive design', () => {
    const { container } = renderWithProviders(<SearchPage />);

    const mainContainer = container.firstChild;
    expect(mainContainer).toHaveClass('max-w-2xl', 'mx-auto', 'p-4', 'pb-20');
  });

  it('handles empty search gracefully', async () => {
    const user = userEvent.setup();
    renderWithProviders(<SearchPage />);

    const searchInput = screen.getByPlaceholderText('Search for people, posts, communities...');
    
    // Try to submit empty search
    const form = searchInput.closest('form');
    fireEvent.submit(form);

    // Should not call setSearchParams for empty query
    expect(mockSetSearchParams).not.toHaveBeenCalled();
  });

  it('autofocuses search input', () => {
    renderWithProviders(<SearchPage />);

    const searchInput = screen.getByPlaceholderText('Search for people, posts, communities...');
    expect(searchInput).toHaveAttribute('autoFocus');
  });
});
