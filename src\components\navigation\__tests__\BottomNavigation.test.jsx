import React from 'react';
import { screen, fireEvent } from '@testing-library/react';
import { renderWithProviders } from '../../../__tests__/utils/testUtils';
import BottomNavigation from '../BottomNavigation';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ pathname: '/feed' }),
}));

describe('BottomNavigation', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('renders all navigation items', () => {
    renderWithProviders(<BottomNavigation />);

    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Search')).toBeInTheDocument();
    expect(screen.getByText('Explore')).toBeInTheDocument();
    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByText('Messages')).toBeInTheDocument();
    expect(screen.getByText('Profile')).toBeInTheDocument();
  });

  it('highlights active navigation item', () => {
    renderWithProviders(<BottomNavigation />, { route: '/feed' });

    const homeLink = screen.getByRole('link', { name: /home/<USER>
    expect(homeLink).toHaveClass('text-primary-600');
  });

  it('shows notification badge when there are unread notifications', () => {
    const initialState = {
      notifications: {
        unreadCount: 5,
        notifications: [],
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<BottomNavigation />, { initialState });

    expect(screen.getByText('5')).toBeInTheDocument();
  });

  it('shows message badge when there are unread messages', () => {
    const initialState = {
      chat: {
        unreadCounts: { chat1: 3, chat2: 2 },
        chats: [],
        activeChat: null,
        isLoading: false,
        error: null,
      },
    };

    renderWithProviders(<BottomNavigation />, { initialState });

    expect(screen.getByText('5')).toBeInTheDocument(); // 3 + 2 = 5
  });

  it('shows 99+ for badges over 99', () => {
    const initialState = {
      notifications: {
        unreadCount: 150,
        notifications: [],
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<BottomNavigation />, { initialState });

    expect(screen.getByText('99+')).toBeInTheDocument();
  });

  it('navigates to correct routes when clicked', () => {
    renderWithProviders(<BottomNavigation />);

    const searchLink = screen.getByRole('link', { name: /search/i });
    expect(searchLink).toHaveAttribute('href', '/search');

    const exploreLink = screen.getByRole('link', { name: /explore/i });
    expect(exploreLink).toHaveAttribute('href', '/explore');

    const notificationsLink = screen.getByRole('link', { name: /notifications/i });
    expect(notificationsLink).toHaveAttribute('href', '/notifications');

    const messagesLink = screen.getByRole('link', { name: /messages/i });
    expect(messagesLink).toHaveAttribute('href', '/messages');
  });

  it('generates dynamic profile link based on current user', () => {
    const initialState = {
      auth: {
        user: {
          id: '1',
          username: 'johndoe',
          email: '<EMAIL>',
          name: 'John Doe',
        },
        token: 'test-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
        message: null,
      },
    };

    renderWithProviders(<BottomNavigation />, { initialState });

    const profileLink = screen.getByRole('link', { name: /profile/i });
    expect(profileLink).toHaveAttribute('href', '/profile/johndoe');
  });

  it('falls back to /profile when user has no username', () => {
    const initialState = {
      auth: {
        user: {
          id: '1',
          username: null,
          email: '<EMAIL>',
          name: 'John Doe',
        },
        token: 'test-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
        message: null,
      },
    };

    renderWithProviders(<BottomNavigation />, { initialState });

    const profileLink = screen.getByRole('link', { name: /profile/i });
    expect(profileLink).toHaveAttribute('href', '/profile');
  });

  it('is hidden on large screens', () => {
    const { container } = renderWithProviders(<BottomNavigation />);

    const navigation = container.firstChild;
    expect(navigation).toHaveClass('lg:hidden');
  });

  it('is fixed at bottom of screen', () => {
    const { container } = renderWithProviders(<BottomNavigation />);

    const navigation = container.firstChild;
    expect(navigation).toHaveClass('fixed', 'bottom-0', 'left-0', 'right-0');
  });

  it('has proper z-index for layering', () => {
    const { container } = renderWithProviders(<BottomNavigation />);

    const navigation = container.firstChild;
    expect(navigation).toHaveClass('z-50');
  });

  it('shows active indicator for current route', () => {
    renderWithProviders(<BottomNavigation />, { route: '/feed' });

    // Check for active indicator (small dot at bottom)
    const activeIndicator = document.querySelector('.absolute.bottom-0');
    expect(activeIndicator).toBeInTheDocument();
  });

  describe('Route Detection', () => {
    it('detects feed route as active', () => {
      renderWithProviders(<BottomNavigation />, { route: '/feed' });

      const homeLink = screen.getByRole('link', { name: /home/<USER>
      expect(homeLink).toHaveClass('text-primary-600');
    });

    it('detects search route as active', () => {
      renderWithProviders(<BottomNavigation />, { route: '/search' });

      const searchLink = screen.getByRole('link', { name: /search/i });
      expect(searchLink).toHaveClass('text-primary-600');
    });

    it('detects explore route as active', () => {
      renderWithProviders(<BottomNavigation />, { route: '/explore' });

      const exploreLink = screen.getByRole('link', { name: /explore/i });
      expect(exploreLink).toHaveClass('text-primary-600');
    });

    it('detects notifications route as active', () => {
      renderWithProviders(<BottomNavigation />, { route: '/notifications' });

      const notificationsLink = screen.getByRole('link', { name: /notifications/i });
      expect(notificationsLink).toHaveClass('text-primary-600');
    });

    it('detects messages route as active', () => {
      renderWithProviders(<BottomNavigation />, { route: '/messages' });

      const messagesLink = screen.getByRole('link', { name: /messages/i });
      expect(messagesLink).toHaveClass('text-primary-600');
    });

    it('detects profile routes as active', () => {
      renderWithProviders(<BottomNavigation />, { route: '/profile/johndoe' });

      const profileLink = screen.getByRole('link', { name: /profile/i });
      expect(profileLink).toHaveClass('text-primary-600');
    });

    it('detects nested profile routes as active', () => {
      renderWithProviders(<BottomNavigation />, { route: '/profile/johndoe/followers' });

      const profileLink = screen.getByRole('link', { name: /profile/i });
      expect(profileLink).toHaveClass('text-primary-600');
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      renderWithProviders(<BottomNavigation />);

      const links = screen.getAllByRole('link');
      links.forEach(link => {
        expect(link).toHaveAttribute('href');
      });
    });

    it('has semantic navigation structure', () => {
      const { container } = renderWithProviders(<BottomNavigation />);

      const navigation = container.firstChild;
      expect(navigation).toBeInTheDocument();
    });

    it('maintains focus order', () => {
      renderWithProviders(<BottomNavigation />);

      const links = screen.getAllByRole('link');
      expect(links).toHaveLength(6);

      // Check that links are in expected order
      expect(links[0]).toHaveTextContent('Home');
      expect(links[1]).toHaveTextContent('Search');
      expect(links[2]).toHaveTextContent('Explore');
      expect(links[3]).toHaveTextContent('Notifications');
      expect(links[4]).toHaveTextContent('Messages');
      expect(links[5]).toHaveTextContent('Profile');
    });
  });

  describe('Dark Mode Support', () => {
    it('has dark mode classes', () => {
      const { container } = renderWithProviders(<BottomNavigation />);

      const navigation = container.firstChild;
      expect(navigation).toHaveClass('dark:bg-dark-800', 'dark:border-dark-700');
    });
  });
});
