import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  DocumentTextIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  PlusIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';

const DraftPostsPage = () => {
  const navigate = useNavigate();
  const [drafts, setDrafts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDrafts, setSelectedDrafts] = useState(new Set());

  // Load drafts from localStorage (mock data)
  useEffect(() => {
    const loadDrafts = () => {
      const savedDrafts = localStorage.getItem('draftPosts');
      if (savedDrafts) {
        setDrafts(JSON.parse(savedDrafts));
      } else {
        // Mock draft data
        const mockDrafts = [
          {
            id: '1',
            content: 'This is my first draft post about technology trends in 2024...',
            title: 'Tech Trends 2024',
            createdAt: '2024-01-15T10:30:00Z',
            updatedAt: '2024-01-15T14:45:00Z',
            wordCount: 156,
            tags: ['technology', 'trends', '2024'],
            community: null,
          },
          {
            id: '2',
            content: 'Working on a new design system for our company. Here are some thoughts on component architecture...',
            title: 'Design System Thoughts',
            createdAt: '2024-01-14T09:15:00Z',
            updatedAt: '2024-01-14T16:20:00Z',
            wordCount: 89,
            tags: ['design', 'system', 'components'],
            community: 'design-community',
          },
          {
            id: '3',
            content: 'Just finished reading an amazing book about productivity. Key takeaways include...',
            title: 'Productivity Book Review',
            createdAt: '2024-01-13T20:00:00Z',
            updatedAt: '2024-01-13T20:30:00Z',
            wordCount: 234,
            tags: ['books', 'productivity', 'review'],
            community: null,
          },
        ];
        setDrafts(mockDrafts);
        localStorage.setItem('draftPosts', JSON.stringify(mockDrafts));
      }
      setIsLoading(false);
    };

    loadDrafts();
  }, []);

  // Save drafts to localStorage
  const saveDrafts = (updatedDrafts) => {
    setDrafts(updatedDrafts);
    localStorage.setItem('draftPosts', JSON.stringify(updatedDrafts));
  };

  // Delete draft
  const deleteDraft = (draftId) => {
    const updatedDrafts = drafts.filter(draft => draft.id !== draftId);
    saveDrafts(updatedDrafts);
  };

  // Bulk delete selected drafts
  const deleteSelectedDrafts = () => {
    const updatedDrafts = drafts.filter(draft => !selectedDrafts.has(draft.id));
    saveDrafts(updatedDrafts);
    setSelectedDrafts(new Set());
  };

  // Publish draft (mock function)
  const publishDraft = (draftId) => {
    // In a real app, this would make an API call to publish the post
    console.log('Publishing draft:', draftId);
    // Remove from drafts after publishing
    deleteDraft(draftId);
    // Show success message (you could use toast here)
    alert('Draft published successfully!');
  };

  // Toggle draft selection
  const toggleDraftSelection = (draftId) => {
    const newSelected = new Set(selectedDrafts);
    if (newSelected.has(draftId)) {
      newSelected.delete(draftId);
    } else {
      newSelected.add(draftId);
    }
    setSelectedDrafts(newSelected);
  };

  // Select all drafts
  const selectAllDrafts = () => {
    if (selectedDrafts.size === drafts.length) {
      setSelectedDrafts(new Set());
    } else {
      setSelectedDrafts(new Set(drafts.map(draft => draft.id)));
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Truncate content
  const truncateContent = (content, maxLength = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="max-w-4xl mx-auto p-4 pb-20"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <DocumentTextIcon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Draft Posts
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {drafts.length} draft{drafts.length !== 1 ? 's' : ''}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {selectedDrafts.size > 0 && (
            <button
              onClick={deleteSelectedDrafts}
              className="px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 border border-red-300 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
            >
              Delete Selected ({selectedDrafts.size})
            </button>
          )}
          
          <button
            onClick={() => navigate('/create-post')}
            className="flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            <PlusIcon className="h-4 w-4" />
            <span>New Draft</span>
          </button>
        </div>
      </div>

      {/* Bulk Actions */}
      {drafts.length > 0 && (
        <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={selectedDrafts.size === drafts.length && drafts.length > 0}
              onChange={selectAllDrafts}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Select all drafts
            </span>
          </label>
          
          {selectedDrafts.size > 0 && (
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {selectedDrafts.size} of {drafts.length} selected
            </span>
          )}
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="space-y-4">
          {[...Array(3)].map((_, index) => (
            <div
              key={index}
              className="bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700 animate-pulse"
            >
              <div className="flex items-start space-x-4">
                <div className="w-4 h-4 bg-gray-200 dark:bg-dark-700 rounded"></div>
                <div className="flex-1 space-y-3">
                  <div className="h-4 bg-gray-200 dark:bg-dark-700 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 dark:bg-dark-700 rounded w-full"></div>
                  <div className="h-3 bg-gray-200 dark:bg-dark-700 rounded w-2/3"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && drafts.length === 0 && (
        <div className="text-center py-12">
          <DocumentTextIcon className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No drafts yet
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            Start writing your first draft post to see it here.
          </p>
          <button
            onClick={() => navigate('/create-post')}
            className="inline-flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            <PlusIcon className="h-4 w-4" />
            <span>Create Your First Draft</span>
          </button>
        </div>
      )}

      {/* Drafts List */}
      {!isLoading && drafts.length > 0 && (
        <div className="space-y-4">
          {drafts.map((draft) => (
            <motion.div
              key={draft.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start space-x-4">
                {/* Selection Checkbox */}
                <input
                  type="checkbox"
                  checked={selectedDrafts.has(draft.id)}
                  onChange={() => toggleDraftSelection(draft.id)}
                  className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />

                {/* Draft Content */}
                <div className="flex-1 min-w-0">
                  {/* Title */}
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {draft.title || 'Untitled Draft'}
                  </h3>

                  {/* Content Preview */}
                  <p className="text-gray-600 dark:text-gray-300 mb-3 leading-relaxed">
                    {truncateContent(draft.content)}
                  </p>

                  {/* Tags */}
                  {draft.tags && draft.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-3">
                      {draft.tags.map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-1 text-xs bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  )}

                  {/* Community */}
                  {draft.community && (
                    <div className="flex items-center space-x-2 mb-3">
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        Community:
                      </span>
                      <span className="text-sm font-medium text-primary-600 dark:text-primary-400">
                        {draft.community}
                      </span>
                    </div>
                  )}

                  {/* Meta Information */}
                  <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                    <span>{draft.wordCount} words</span>
                    <span>•</span>
                    <span>Created {formatDate(draft.createdAt)}</span>
                    {draft.updatedAt !== draft.createdAt && (
                      <>
                        <span>•</span>
                        <span>Updated {formatDate(draft.updatedAt)}</span>
                      </>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => navigate(`/create-post?draft=${draft.id}`)}
                    className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 rounded-lg transition-colors"
                    title="Edit draft"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  
                  <button
                    onClick={() => navigate(`/preview-post?draft=${draft.id}`)}
                    className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 rounded-lg transition-colors"
                    title="Preview draft"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </button>
                  
                  <button
                    onClick={() => navigate(`/schedule-post?draft=${draft.id}`)}
                    className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 rounded-lg transition-colors"
                    title="Schedule draft"
                  >
                    <CalendarIcon className="h-4 w-4" />
                  </button>
                  
                  <button
                    onClick={() => publishDraft(draft.id)}
                    className="px-3 py-1 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    title="Publish now"
                  >
                    Publish
                  </button>
                  
                  <button
                    onClick={() => deleteDraft(draft.id)}
                    className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                    title="Delete draft"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default DraftPostsPage;
