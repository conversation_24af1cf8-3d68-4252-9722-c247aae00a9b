import { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  SparklesIcon,
  XMarkIcon,
  PaperAirplaneIcon,
  LightBulbIcon,
  PencilIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  PhotoIcon,
} from '@heroicons/react/24/outline';
import { Button } from '../ui/Button';

const AIAssistant = ({ isOpen, onClose, onContentGenerated, context = null }) => {
  const { user } = useSelector((state) => state.auth);
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'ai',
      content: "Hi! I'm your AI assistant. I can help you create engaging content, improve your writing, generate ideas, and much more. What would you like to work on today?",
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState(null);
  const messagesEndRef = useRef(null);

  const aiFeatures = [
    {
      id: 'content-generation',
      name: 'Content Generation',
      icon: PencilIcon,
      description: 'Generate posts, captions, and content ideas',
      prompts: [
        'Write a motivational post about productivity',
        'Create a funny caption for my photo',
        'Generate ideas for my business content',
        'Write a professional announcement'
      ]
    },
    {
      id: 'writing-improvement',
      name: 'Writing Enhancement',
      icon: DocumentTextIcon,
      description: 'Improve grammar, tone, and clarity',
      prompts: [
        'Make this text more professional',
        'Improve the grammar and flow',
        'Make this sound more engaging',
        'Simplify this for better understanding'
      ]
    },
    {
      id: 'idea-brainstorming',
      name: 'Idea Brainstorming',
      icon: LightBulbIcon,
      description: 'Generate creative ideas and concepts',
      prompts: [
        'Give me content ideas for my niche',
        'Suggest hashtags for my post',
        'Help me plan a content calendar',
        'Generate discussion topics'
      ]
    },
    {
      id: 'conversation-starter',
      name: 'Conversation Starters',
      icon: ChatBubbleLeftRightIcon,
      description: 'Create engaging questions and discussions',
      prompts: [
        'Create a poll question about technology',
        'Generate icebreaker questions',
        'Suggest debate topics',
        'Create community discussion prompts'
      ]
    },
    {
      id: 'image-analysis',
      name: 'Image Description',
      icon: PhotoIcon,
      description: 'Generate captions and descriptions for images',
      prompts: [
        'Describe this image professionally',
        'Create an engaging caption',
        'Generate alt text for accessibility',
        'Write a story about this image'
      ]
    }
  ];

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const simulateAIResponse = async (userMessage) => {
    setIsTyping(true);
    
    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000));
    
    let aiResponse = '';
    
    // Simple AI response simulation based on keywords
    const message = userMessage.toLowerCase();
    
    if (message.includes('post') || message.includes('content')) {
      aiResponse = `Here's a great post idea for you:

"🌟 Success isn't just about reaching your destination, it's about who you become along the journey. Every challenge you face today is building the strength you'll need tomorrow.

What's one small step you're taking today toward your goals? Share below! 👇

#Motivation #Growth #Success #Journey"

Would you like me to adjust the tone or create variations of this post?`;
    } else if (message.includes('improve') || message.includes('better')) {
      aiResponse = `I'd be happy to help improve your content! Here are some suggestions:

✅ Use active voice instead of passive
✅ Add specific examples and details
✅ Include a clear call-to-action
✅ Break up long paragraphs for readability
✅ Add relevant emojis for engagement

Please share the text you'd like me to improve, and I'll provide specific suggestions!`;
    } else if (message.includes('idea') || message.includes('brainstorm')) {
      aiResponse = `Here are some creative content ideas for you:

💡 **Trending Topics:**
• "Behind the scenes" of your daily routine
• "Lessons learned" from recent experiences
• "Unpopular opinion" posts that spark discussion
• "Then vs Now" comparison posts

💡 **Engagement Boosters:**
• Ask "This or That" questions
• Share your biggest failure and lesson learned
• Create "Fill in the blank" posts
• Host virtual Q&A sessions

Which type of content resonates most with your audience?`;
    } else if (message.includes('hashtag')) {
      aiResponse = `Here are some effective hashtag strategies:

🏷️ **Mix of hashtag types:**
• 3-5 popular hashtags (#motivation #success)
• 5-10 medium hashtags (#entrepreneurlife #mindsetmatters)
• 10-15 niche hashtags (#solopreneurjourney #productivityhacks)

🏷️ **Pro tips:**
• Research hashtags your audience uses
• Create a branded hashtag for your community
• Use location-based hashtags when relevant
• Avoid banned or overused hashtags

What's your niche? I can suggest specific hashtags for your content!`;
    } else {
      aiResponse = `I understand you're looking for help with: "${userMessage}"

I can assist you with:
• Creating engaging content and posts
• Improving your writing style and grammar
• Generating creative ideas and concepts
• Suggesting hashtags and captions
• Analyzing and describing images
• Creating conversation starters

What specific type of assistance would be most helpful for you right now?`;
    }

    setIsTyping(false);
    
    const newMessage = {
      id: Date.now(),
      type: 'ai',
      content: aiResponse,
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, newMessage]);
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!inputMessage.trim()) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');

    await simulateAIResponse(inputMessage);
  };

  const handleQuickPrompt = async (prompt) => {
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: prompt,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    await simulateAIResponse(prompt);
  };

  const handleUseContent = (content) => {
    onContentGenerated?.(content);
    toast.success('Content added to your post!');
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="bg-background rounded-lg shadow-xl border border-gray-800 w-full max-w-4xl h-[80vh] flex flex-col"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-800">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
              <SparklesIcon className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-white">AI Assistant</h2>
              <p className="text-sm text-gray-400">Powered by advanced AI</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Features Sidebar */}
          <div className="w-80 border-r border-gray-800 p-4 overflow-y-auto">
            <h3 className="text-lg font-medium text-white mb-4">AI Features</h3>
            <div className="space-y-3">
              {aiFeatures.map((feature) => (
                <button
                  key={feature.id}
                  onClick={() => setSelectedFeature(feature)}
                  className={`w-full text-left p-3 rounded-lg border transition-colors ${
                    selectedFeature?.id === feature.id
                      ? 'border-primary-500 bg-primary-500/10'
                      : 'border-gray-700 hover:border-gray-600'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <feature.icon className="h-5 w-5 text-primary-400 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-white text-sm">{feature.name}</h4>
                      <p className="text-xs text-gray-400 mt-1">{feature.description}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>

            {/* Quick Prompts */}
            {selectedFeature && (
              <div className="mt-6">
                <h4 className="text-sm font-medium text-white mb-3">Quick Prompts</h4>
                <div className="space-y-2">
                  {selectedFeature.prompts.map((prompt, index) => (
                    <button
                      key={index}
                      onClick={() => handleQuickPrompt(prompt)}
                      className="w-full text-left p-2 text-sm text-gray-300 hover:text-white hover:bg-gray-800 rounded transition-colors"
                    >
                      "{prompt}"
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Chat Area */}
          <div className="flex-1 flex flex-col">
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] p-3 rounded-lg ${
                      message.type === 'user'
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-800 text-gray-100'
                    }`}
                  >
                    <p className="whitespace-pre-wrap">{message.content}</p>
                    {message.type === 'ai' && (
                      <div className="mt-3 pt-3 border-t border-gray-700">
                        <Button
                          onClick={() => handleUseContent(message.content)}
                          size="sm"
                          variant="secondary"
                          className="text-xs"
                        >
                          Use This Content
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              
              {isTyping && (
                <div className="flex justify-start">
                  <div className="bg-gray-800 text-gray-100 p-3 rounded-lg">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <div className="p-4 border-t border-gray-800">
              <form onSubmit={handleSendMessage} className="flex space-x-2">
                <input
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="Ask me anything..."
                  className="flex-1 p-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  disabled={isTyping}
                />
                <Button
                  type="submit"
                  disabled={!inputMessage.trim() || isTyping}
                  className="px-4"
                >
                  <PaperAirplaneIcon className="h-5 w-5" />
                </Button>
              </form>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default AIAssistant;
