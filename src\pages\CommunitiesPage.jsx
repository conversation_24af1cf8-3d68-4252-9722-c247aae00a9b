import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { fetchCommunities } from '../store/slices/communitySlice';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { PlusIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { CommunitiesLoader } from '../components/ui/LoadingSpinner';

const CommunitiesPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { communities, isLoading, totalPages, currentPage } = useSelector(
    (state) => state.community
  );
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [activeTab, setActiveTab] = useState('discover');

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  useEffect(() => {
    dispatch(fetchCommunities({ page: currentPage, search: debouncedSearch }));
  }, [dispatch, currentPage, debouncedSearch]);

  const tabs = [
    { id: 'discover', label: 'Discover', icon: '🔍' },
    { id: 'joined', label: 'My Communities', icon: '👥' },
    { id: 'created', label: 'Created by Me', icon: '⭐' },
  ];

  const filteredCommunities = activeTab === 'joined' 
    ? communities.filter(c => c.isJoined)
    : activeTab === 'created'
    ? communities.filter(c => c.isAdmin)
    : communities;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="max-w-7xl mx-auto p-4"
    >
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Communities
        </h1>
        <Button
          onClick={() => navigate('/communities/create')}
          className="flex items-center space-x-2"
        >
          <PlusIcon className="h-5 w-5" />
          <span>Create Community</span>
        </Button>
      </div>

      <div className="mb-6">
        <div className="relative">
          <Input
            type="text"
            placeholder="Search communities..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10"
          />
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
        </div>
      </div>

      <div className="mb-6">
        <div className="border-b border-gray-200 dark:border-dark-700">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {isLoading ? (
        <CommunitiesLoader />
      ) : filteredCommunities.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCommunities.map((community) => (
            <motion.div
              key={community._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-dark-800 rounded-lg shadow-sm overflow-hidden"
            >
              <div className="h-32 bg-gradient-to-r from-primary-400 to-primary-600 relative">
                {community.image && (
                  <img
                    src={community.image}
                    alt={community.name}
                    className="w-full h-full object-cover"
                  />
                )}
                <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                  <h3 className="text-2xl font-bold text-white">{community.name}</h3>
                </div>
              </div>
              <div className="p-4">
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {community.description}
                </p>
                <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                  <span>{community.members?.length || 0} members</span>
                  <span>{community.category}</span>
                </div>
                <div className="mt-4">
                  <Button
                    onClick={() => navigate(`/communities/${community.slug}`)}
                    className="w-full"
                  >
                    {community.isJoined ? 'View Community' : 'Join Community'}
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">👥</div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            No communities found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {activeTab === 'discover'
              ? 'Be the first to create a community'
              : activeTab === 'joined'
              ? 'Join some communities to see them here'
              : 'Create your first community'}
          </p>
          {activeTab !== 'joined' && (
            <Button onClick={() => navigate('/communities/create')}>
              Create Community
            </Button>
          )}
        </div>
      )}

      {totalPages > 1 && (
        <div className="mt-8 flex justify-center">
          <nav className="flex items-center space-x-2">
            {[...Array(totalPages)].map((_, index) => (
              <button
                key={index}
                onClick={() => dispatch(fetchCommunities({ page: index + 1, search: debouncedSearch }))}
                className={`px-3 py-1 rounded-md ${
                  currentPage === index + 1
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-dark-700'
                }`}
              >
                {index + 1}
              </button>
            ))}
          </nav>
        </div>
      )}
    </motion.div>
  );
};

export default CommunitiesPage;
