# Production Environment Configuration for NeTuArk

# API Configuration
VITE_API_BASE_URL=https://api.netuark.com/api
VITE_APP_URL=https://netuark.com

# Authentication
VITE_TOKEN_STORAGE_KEY=netuark_access_token
VITE_TOKEN_EXPIRY_BUFFER=300000

# PWA Configuration
VITE_VAPID_PUBLIC_KEY=your-vapid-public-key-here

# Analytics
VITE_GA_TRACKING_ID=G-XXXXXXXXXX
VITE_HOTJAR_ID=your-hotjar-id

# Error Tracking
VITE_SENTRY_DSN=your-sentry-dsn-here

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_TRACKING=true
VITE_ENABLE_PWA=true
VITE_ENABLE_PUSH_NOTIFICATIONS=true

# Performance
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_CODE_SPLITTING=true

# Security
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true

# CDN Configuration
VITE_CDN_URL=https://cdn.netuark.com
VITE_STATIC_ASSETS_URL=https://static.netuark.com

# Social Media
VITE_FACEBOOK_APP_ID=your-facebook-app-id
VITE_TWITTER_API_KEY=your-twitter-api-key
VITE_GOOGLE_CLIENT_ID=your-google-client-id

# File Upload
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,video/mp4,video/webm

# Rate Limiting
VITE_API_RATE_LIMIT=100
VITE_API_RATE_WINDOW=900000

# Cache Configuration
VITE_CACHE_VERSION=1.0.0
VITE_CACHE_MAX_AGE=86400000
