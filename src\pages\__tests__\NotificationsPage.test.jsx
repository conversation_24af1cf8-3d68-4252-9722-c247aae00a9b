import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, mockNotifications } from '../../__tests__/utils/testUtils';
import NotificationsPage from '../NotificationsPage';

// Mock the notification actions
const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: () => mockDispatch,
}));

describe('NotificationsPage', () => {
  beforeEach(() => {
    mockDispatch.mockClear();
  });

  it('renders page header correctly', () => {
    renderWithProviders(<NotificationsPage />);

    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByText('0 unread notifications')).toBeInTheDocument();
  });

  it('displays unread count in header', () => {
    const initialState = {
      notifications: {
        notifications: mockNotifications,
        unreadCount: 1,
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    expect(screen.getByText('1 unread notification')).toBeInTheDocument();
  });

  it('renders filter tabs', () => {
    renderWithProviders(<NotificationsPage />);

    expect(screen.getByText('All')).toBeInTheDocument();
    expect(screen.getByText('Unread')).toBeInTheDocument();
    expect(screen.getByText('Mentions')).toBeInTheDocument();
  });

  it('dispatches fetchNotifications on mount', () => {
    renderWithProviders(<NotificationsPage />);

    expect(mockDispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: expect.stringContaining('fetchNotifications'),
      })
    );
  });

  it('shows loading state', () => {
    const initialState = {
      notifications: {
        notifications: [],
        unreadCount: 0,
        loading: true,
        error: null,
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    // Check for loading animation class
    const loadingElements = document.querySelectorAll('.animate-pulse');
    expect(loadingElements.length).toBeGreaterThan(0);
  });

  it('shows error message when there is an error', () => {
    const initialState = {
      notifications: {
        notifications: [],
        unreadCount: 0,
        loading: false,
        error: 'Failed to fetch notifications',
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    expect(screen.getByText('Failed to fetch notifications')).toBeInTheDocument();
  });

  it('shows empty state when no notifications', () => {
    const initialState = {
      notifications: {
        notifications: [],
        unreadCount: 0,
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    expect(screen.getByText('No notifications')).toBeInTheDocument();
    expect(screen.getByText("You're all caught up!")).toBeInTheDocument();
  });

  it('renders notifications list', () => {
    const initialState = {
      notifications: {
        notifications: mockNotifications,
        unreadCount: 1,
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    expect(screen.getByText('John Doe liked your post')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith commented on your post')).toBeInTheDocument();
  });

  it('highlights unread notifications', () => {
    const initialState = {
      notifications: {
        notifications: mockNotifications,
        unreadCount: 1,
        loading: false,
        error: null,
      },
    };

    const { container } = renderWithProviders(<NotificationsPage />, { initialState });

    // First notification is unread, should have ring styling
    const unreadNotification = container.querySelector('.ring-2');
    expect(unreadNotification).toBeInTheDocument();
  });

  it('shows mark all as read button when there are unread notifications', () => {
    const initialState = {
      notifications: {
        notifications: mockNotifications,
        unreadCount: 1,
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    expect(screen.getByText('Mark all as read')).toBeInTheDocument();
  });

  it('handles mark all as read action', async () => {
    const user = userEvent.setup();
    const initialState = {
      notifications: {
        notifications: mockNotifications,
        unreadCount: 1,
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    const markAllButton = screen.getByText('Mark all as read');
    await user.click(markAllButton);

    expect(mockDispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: expect.stringContaining('markAllAsRead'),
      })
    );
  });

  it('handles individual notification mark as read', async () => {
    const user = userEvent.setup();
    const initialState = {
      notifications: {
        notifications: mockNotifications,
        unreadCount: 1,
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    const markAsReadButtons = screen.getAllByTitle('Mark as read');
    await user.click(markAsReadButtons[0]);

    expect(mockDispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: expect.stringContaining('markAsRead'),
      })
    );
  });

  it('handles notification deletion', async () => {
    const user = userEvent.setup();
    const initialState = {
      notifications: {
        notifications: mockNotifications,
        unreadCount: 1,
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    const deleteButtons = screen.getAllByTitle('Delete notification');
    await user.click(deleteButtons[0]);

    expect(mockDispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: expect.stringContaining('deleteNotification'),
      })
    );
  });

  it('handles bulk selection', async () => {
    const user = userEvent.setup();
    const initialState = {
      notifications: {
        notifications: mockNotifications,
        unreadCount: 1,
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    const checkboxes = screen.getAllByRole('checkbox');
    await user.click(checkboxes[0]); // First notification checkbox

    // Should show bulk action buttons
    expect(screen.getByTitle('Mark selected as read')).toBeInTheDocument();
    expect(screen.getByTitle('Delete selected')).toBeInTheDocument();
  });

  it('filters notifications by type', async () => {
    const user = userEvent.setup();
    const mentionNotification = {
      id: '3',
      type: 'mention',
      message: 'You were mentioned in a post',
      read: false,
      createdAt: '2024-01-13T12:00:00Z',
      link: '/posts/3',
    };

    const initialState = {
      notifications: {
        notifications: [...mockNotifications, mentionNotification],
        unreadCount: 2,
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    // Click on Mentions tab
    const mentionsTab = screen.getByText('Mentions');
    await user.click(mentionsTab);

    // Should only show mention notifications
    expect(screen.getByText('You were mentioned in a post')).toBeInTheDocument();
    expect(screen.queryByText('John Doe liked your post')).not.toBeInTheDocument();
  });

  it('filters unread notifications', async () => {
    const user = userEvent.setup();
    const initialState = {
      notifications: {
        notifications: mockNotifications,
        unreadCount: 1,
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    // Click on Unread tab
    const unreadTab = screen.getByText('Unread');
    await user.click(unreadTab);

    // Should only show unread notifications
    expect(screen.getByText('John Doe liked your post')).toBeInTheDocument();
    expect(screen.queryByText('Jane Smith commented on your post')).not.toBeInTheDocument();
  });

  it('shows correct notification icons', () => {
    const initialState = {
      notifications: {
        notifications: mockNotifications,
        unreadCount: 1,
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    // Check for emoji icons
    expect(screen.getByText('❤️')).toBeInTheDocument(); // like icon
    expect(screen.getByText('💬')).toBeInTheDocument(); // comment icon
  });

  it('formats time correctly', () => {
    const recentNotification = {
      id: '4',
      type: 'like',
      message: 'Recent notification',
      read: false,
      createdAt: new Date(Date.now() - 30000).toISOString(), // 30 seconds ago
      link: '/posts/4',
    };

    const initialState = {
      notifications: {
        notifications: [recentNotification],
        unreadCount: 1,
        loading: false,
        error: null,
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    expect(screen.getByText('Just now')).toBeInTheDocument();
  });

  it('clears error after timeout', async () => {
    const initialState = {
      notifications: {
        notifications: [],
        unreadCount: 0,
        loading: false,
        error: 'Test error',
      },
    };

    renderWithProviders(<NotificationsPage />, { initialState });

    expect(screen.getByText('Test error')).toBeInTheDocument();

    // Wait for error to be cleared
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: expect.stringContaining('clearError'),
        })
      );
    }, { timeout: 6000 });
  });

  it('has proper responsive design classes', () => {
    const { container } = renderWithProviders(<NotificationsPage />);

    const mainContainer = container.firstChild;
    expect(mainContainer).toHaveClass('max-w-2xl', 'mx-auto', 'p-4', 'pb-20');
  });
});
