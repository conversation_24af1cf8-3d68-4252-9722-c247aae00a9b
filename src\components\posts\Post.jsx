import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';
import {
  HeartIcon,
  ChatBubbleLeftIcon,
  ShareIcon,
  BookmarkIcon,
  EllipsisHorizontalIcon,
  MapPinIcon,
  CalendarIcon,
  ClockIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';
import {
  HeartIcon as HeartIconSolid,
  BookmarkIcon as BookmarkIconSolid,
} from '@heroicons/react/24/solid';
import { Button } from '../ui/Button';
import { likePost, sharePost } from '../../store/slices/feedSlice';
import PostComments from './PostComments';

const Post = ({ post, onViewComments, showComments = false }) => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const [isLiked, setIsLiked] = useState(post.isLiked || false);
  const [likesCount, setLikesCount] = useState(post.likesCount || 0);
  const [isBookmarked, setIsBookmarked] = useState(post.isBookmarked || false);
  const [showFullContent, setShowFullContent] = useState(false);
  const [selectedPollOptions, setSelectedPollOptions] = useState([]);
  const [hasVoted, setHasVoted] = useState(post.poll?.hasVoted || false);

  const handleLike = async () => {
    try {
      setIsLiked(!isLiked);
      setLikesCount(prev => isLiked ? prev - 1 : prev + 1);
      await dispatch(likePost(post._id));
    } catch (error) {
      // Revert on error
      setIsLiked(isLiked);
      setLikesCount(likesCount);
    }
  };

  const handleShare = async () => {
    try {
      await dispatch(sharePost(post._id));
      // Could show a share modal or copy link to clipboard
      navigator.clipboard?.writeText(`${window.location.origin}/post/${post._id}`);
    } catch (error) {
      console.error('Failed to share post:', error);
    }
  };

  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked);
    // TODO: Implement bookmark functionality
  };

  const handlePollVote = () => {
    if (hasVoted || selectedPollOptions.length === 0) return;
    
    // TODO: Submit poll vote to backend
    setHasVoted(true);
    console.log('Voting for options:', selectedPollOptions);
  };

  const handlePollOptionSelect = (optionIndex) => {
    if (hasVoted) return;

    if (post.poll?.allowMultipleChoices) {
      setSelectedPollOptions(prev => 
        prev.includes(optionIndex) 
          ? prev.filter(i => i !== optionIndex)
          : [...prev, optionIndex]
      );
    } else {
      setSelectedPollOptions([optionIndex]);
    }
  };

  const formatContent = (content) => {
    if (!content) return '';
    
    const maxLength = 300;
    if (content.length <= maxLength || showFullContent) {
      return content;
    }
    
    return content.substring(0, maxLength) + '...';
  };

  const renderMedia = () => {
    if (!post.media || post.media.length === 0) return null;

    return (
      <div className={`mt-4 ${post.media.length === 1 ? '' : 'grid grid-cols-2 gap-2'}`}>
        {post.media.map((media, index) => (
          <div key={index} className="relative">
            {media.type === 'image' ? (
              <img
                src={media.url}
                alt="Post media"
                className="w-full h-64 object-cover rounded-lg"
              />
            ) : (
              <video
                src={media.url}
                controls
                className="w-full h-64 object-cover rounded-lg"
              />
            )}
          </div>
        ))}
      </div>
    );
  };

  const renderPoll = () => {
    if (post.type !== 'poll' || !post.poll) return null;

    const totalVotes = post.poll.totalVotes || 0;

    return (
      <div className="mt-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700">
        <div className="space-y-3">
          {post.poll.options.map((option, index) => {
            const votes = post.poll.votes?.[index]?.count || 0;
            const percentage = totalVotes > 0 ? (votes / totalVotes) * 100 : 0;
            const isSelected = selectedPollOptions.includes(index);

            return (
              <div key={index} className="relative">
                <button
                  onClick={() => handlePollOptionSelect(index)}
                  disabled={hasVoted}
                  className={`w-full text-left p-3 rounded-lg border transition-colors ${
                    hasVoted
                      ? 'cursor-not-allowed bg-gray-800 border-gray-600'
                      : isSelected
                      ? 'bg-primary-600/20 border-primary-500 text-primary-400'
                      : 'bg-gray-800 border-gray-600 hover:border-gray-500'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-white">{option}</span>
                    {hasVoted && (
                      <span className="text-sm text-gray-400">
                        {percentage.toFixed(1)}% ({votes})
                      </span>
                    )}
                  </div>
                  
                  {hasVoted && (
                    <div className="mt-2 w-full bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-primary-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  )}
                </button>
              </div>
            );
          })}
        </div>

        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-400">
            {totalVotes} {totalVotes === 1 ? 'vote' : 'votes'}
            {post.poll.duration && (
              <span className="ml-2">
                • Ends in {post.poll.duration} hours
              </span>
            )}
          </div>
          
          {!hasVoted && selectedPollOptions.length > 0 && (
            <Button
              onClick={handlePollVote}
              size="sm"
              className="px-4 py-1"
            >
              Vote
            </Button>
          )}
        </div>
      </div>
    );
  };

  const renderEvent = () => {
    if (post.type !== 'event' || !post.event) return null;

    return (
      <div className="mt-4 p-4 bg-gradient-to-r from-primary-600/20 to-primary-800/20 rounded-lg border border-primary-500/30">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <CalendarIcon className="h-8 w-8 text-primary-400" />
          </div>
          <div className="flex-1">
            <h4 className="text-lg font-semibold text-white mb-2">
              {post.event.title}
            </h4>
            
            <div className="space-y-2 text-sm text-gray-300">
              <div className="flex items-center space-x-2">
                <ClockIcon className="h-4 w-4" />
                <span>
                  {new Date(post.event.date).toLocaleDateString()} at {post.event.time}
                </span>
              </div>
              
              {post.event.location && (
                <div className="flex items-center space-x-2">
                  <MapPinIcon className="h-4 w-4" />
                  <span>{post.event.location}</span>
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <UserGroupIcon className="h-4 w-4" />
                <span>{post.event.attendees?.length || 0} attending</span>
              </div>
            </div>

            <div className="mt-3">
              <Button
                variant={post.event.isAttending ? 'secondary' : 'primary'}
                size="sm"
                className="px-4 py-1"
              >
                {post.event.isAttending ? 'Going' : 'Interested'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderTags = () => {
    if (!post.tags || post.tags.length === 0) return null;

    return (
      <div className="mt-3 flex flex-wrap gap-2">
        {post.tags.map((tag, index) => (
          <span
            key={index}
            className="inline-block bg-primary-600/20 text-primary-400 px-2 py-1 rounded-full text-xs"
          >
            #{tag}
          </span>
        ))}
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-background rounded-lg shadow-sm border border-gray-800 p-6"
    >
      {/* Post header */}
      <div className="flex items-center space-x-4 mb-4">
        <img
          src={post.author?.avatar || '/default-avatar.png'}
          alt={post.author?.username}
          className="w-12 h-12 rounded-full"
        />
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <h3 className="font-semibold text-white">
              {post.author?.realName || 'Anonymous User'}
            </h3>
            <p className="text-sm text-gray-400">
              @{post.author?.username || 'anonymous'}
            </p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <span>{formatDistanceToNow(new Date(post.createdAt))} ago</span>
            {post.location && (
              <>
                <span>•</span>
                <div className="flex items-center space-x-1">
                  <MapPinIcon className="h-3 w-3" />
                  <span>{post.location}</span>
                </div>
              </>
            )}
          </div>
        </div>
        
        {user?._id !== post.author?._id && (
          <Button
            variant={post.author?.isFollowed ? 'secondary' : 'primary'}
            size="sm"
          >
            {post.author?.isFollowed ? 'Following' : 'Follow'}
          </Button>
        )}
        
        <button className="text-gray-400 hover:text-gray-300">
          <EllipsisHorizontalIcon className="h-5 w-5" />
        </button>
      </div>

      {/* Post content */}
      <div className="mb-4">
        {post.content && (
          <div className="text-white mb-4">
            <p>{formatContent(post.content)}</p>
            {post.content.length > 300 && (
              <button
                onClick={() => setShowFullContent(!showFullContent)}
                className="text-primary-400 hover:text-primary-300 text-sm mt-2"
              >
                {showFullContent ? 'Show less' : 'Show more'}
              </button>
            )}
          </div>
        )}
        
        {renderMedia()}
        {renderPoll()}
        {renderEvent()}
        {renderTags()}
      </div>

      {/* Post actions */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-800">
        <div className="flex space-x-6">
          <button
            onClick={handleLike}
            className="flex items-center space-x-2 text-gray-500 hover:text-red-500 transition-colors"
          >
            {isLiked ? (
              <HeartIconSolid className="h-5 w-5 text-red-500" />
            ) : (
              <HeartIcon className="h-5 w-5" />
            )}
            <span>{likesCount}</span>
          </button>
          
          <button
            onClick={() => onViewComments?.(post._id)}
            className="flex items-center space-x-2 text-gray-500 hover:text-blue-500 transition-colors"
          >
            <ChatBubbleLeftIcon className="h-5 w-5" />
            <span>{post.commentsCount || 0}</span>
          </button>
          
          <button
            onClick={handleShare}
            className="flex items-center space-x-2 text-gray-500 hover:text-green-500 transition-colors"
          >
            <ShareIcon className="h-5 w-5" />
            <span>{post.sharesCount || 0}</span>
          </button>
        </div>
        
        <button
          onClick={handleBookmark}
          className="text-gray-500 hover:text-gray-300 transition-colors"
        >
          {isBookmarked ? (
            <BookmarkIconSolid className="h-5 w-5 text-yellow-500" />
          ) : (
            <BookmarkIcon className="h-5 w-5" />
          )}
        </button>
      </div>

      {/* Comments section */}
      {showComments && (
        <PostComments
          postId={post._id}
          onClose={() => onViewComments?.(null)}
        />
      )}
    </motion.div>
  );
};

export default Post;
