import { Link, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  HomeIcon,
  MagnifyingGlassIcon,
  GlobeAltIcon,
  BellIcon,
  ChatBubbleLeftRightIcon,
  UserCircleIcon,
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  MagnifyingGlassIcon as MagnifyingGlassIconSolid,
  GlobeAltIcon as GlobeAltIconSolid,
  BellIcon as BellIconSolid,
  ChatBubbleLeftRightIcon as ChatBubbleLeftRightIconSolid,
  UserCircleIcon as UserCircleIconSolid,
} from '@heroicons/react/24/solid';

const BottomNavigation = () => {
  const location = useLocation();
  const { user } = useSelector((state) => state.auth);
  const { unreadCounts } = useSelector((state) => state.chat);
  const { unreadCount: notificationCount } = useSelector((state) => state.notifications);

  // Calculate total unread messages
  const totalUnreadMessages = Object.values(unreadCounts || {}).reduce((sum, count) => sum + count, 0);

  // Navigation items configuration
  const navigation = [
    {
      name: 'Home',
      href: '/feed',
      icon: HomeIcon,
      activeIcon: HomeIconSolid,
      isImplemented: true,
    },
    {
      name: 'Search',
      href: '/search',
      icon: MagnifyingGlassIcon,
      activeIcon: MagnifyingGlassIconSolid,
      isImplemented: true, // Now implemented
    },
    {
      name: 'Explore',
      href: '/explore',
      icon: GlobeAltIcon,
      activeIcon: GlobeAltIconSolid,
      isImplemented: true,
    },
    {
      name: 'Notifications',
      href: '/notifications',
      icon: BellIcon,
      activeIcon: BellIconSolid,
      isImplemented: true, // Now implemented
      badge: notificationCount,
    },
    {
      name: 'Messages',
      href: '/messages',
      icon: ChatBubbleLeftRightIcon,
      activeIcon: ChatBubbleLeftRightIconSolid,
      isImplemented: true,
      badge: totalUnreadMessages,
    },
    {
      name: 'Profile',
      href: user?.username ? `/profile/${user.username}` : '/profile',
      icon: UserCircleIcon,
      activeIcon: UserCircleIconSolid,
      isImplemented: true,
    },
  ];

  const isActive = (item) => {
    if (item.href === '/feed' && location.pathname === '/feed') return true;
    if (item.href === '/search' && location.pathname === '/search') return true;
    if (item.href === '/explore' && location.pathname === '/explore') return true;
    if (item.href === '/notifications' && location.pathname === '/notifications') return true;
    if (item.href === '/messages' && location.pathname === '/messages') return true;
    if (item.name === 'Profile' && location.pathname.startsWith('/profile')) return true;
    return false;
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-dark-800 border-t border-gray-200 dark:border-dark-700 lg:hidden">
      <div className="grid grid-cols-6 h-16">
        {navigation.map((item) => {
          const active = isActive(item);
          const IconComponent = active ? item.activeIcon : item.icon;
          
          return (
            <Link
              key={item.name}
              to={item.href}
              className={`flex flex-col items-center justify-center space-y-1 relative ${
                active
                  ? 'text-primary-600 dark:text-primary-400'
                  : item.isImplemented
                  ? 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  : 'text-gray-300 dark:text-gray-600'
              } ${!item.isImplemented ? 'cursor-not-allowed' : ''}`}
              onClick={(e) => {
                if (!item.isImplemented) {
                  e.preventDefault();
                }
              }}
            >
              <div className="relative">
                <IconComponent className="h-6 w-6" />
                
                {/* Badge for notifications and messages */}
                {item.badge && item.badge > 0 && (
                  <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
                    {item.badge > 99 ? '99+' : item.badge}
                  </span>
                )}
                
                {/* Coming Soon indicator for unimplemented features */}
                {!item.isImplemented && (
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full"></div>
                )}
              </div>
              
              <span className={`text-xs ${!item.isImplemented ? 'opacity-50' : ''}`}>
                {item.name}
              </span>
              
              {/* Active indicator */}
              {active && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 dark:bg-primary-400 rounded-full"></div>
              )}
            </Link>
          );
        })}
      </div>
    </div>
  );
};

export default BottomNavigation;
