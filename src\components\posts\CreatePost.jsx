import { useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  PhotoIcon,
  VideoCameraIcon,
  ChartBarIcon,
  CalendarIcon,
  MapPinIcon,
  FaceSmileIcon,
  XMarkIcon,
  GlobeAltIcon,
  UserGroupIcon,
  LockClosedIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';
import { Button } from '../ui/Button';
import { createPost } from '../../store/slices/feedSlice';
import AIAssistant from '../ai/AIAssistant';
import MediaLibrary from '../media/MediaLibrary';

const CreatePost = ({ onClose, initialContent = '', community = null }) => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const { isLoading } = useSelector((state) => state.feed);

  // Post content state
  const [content, setContent] = useState(initialContent);
  const [postType, setPostType] = useState('text'); // text, image, video, poll, event
  const [privacy, setPrivacy] = useState('public'); // public, friends, private
  const [location, setLocation] = useState('');
  const [tags, setTags] = useState([]);
  const [tagInput, setTagInput] = useState('');

  // Media state
  const [mediaFiles, setMediaFiles] = useState([]);
  const [mediaPreview, setMediaPreview] = useState([]);
  const fileInputRef = useRef(null);
  const videoInputRef = useRef(null);

  // Poll state
  const [pollOptions, setPollOptions] = useState(['', '']);
  const [pollDuration, setPollDuration] = useState('24'); // hours
  const [allowMultipleChoices, setAllowMultipleChoices] = useState(false);

  // Event state
  const [eventTitle, setEventTitle] = useState('');
  const [eventDate, setEventDate] = useState('');
  const [eventTime, setEventTime] = useState('');
  const [eventLocation, setEventLocation] = useState('');

  // Scheduling state
  const [isScheduled, setIsScheduled] = useState(false);
  const [scheduledDate, setScheduledDate] = useState('');
  const [scheduledTime, setScheduledTime] = useState('');

  // AI Assistant state
  const [showAIAssistant, setShowAIAssistant] = useState(false);

  // Media Library state
  const [showMediaLibrary, setShowMediaLibrary] = useState(false);

  const postTypes = [
    { id: 'text', label: 'Text', icon: FaceSmileIcon, description: 'Share your thoughts' },
    { id: 'image', label: 'Photo', icon: PhotoIcon, description: 'Share images' },
    { id: 'video', label: 'Video', icon: VideoCameraIcon, description: 'Share videos' },
    { id: 'poll', label: 'Poll', icon: ChartBarIcon, description: 'Ask your audience' },
    { id: 'event', label: 'Event', icon: CalendarIcon, description: 'Create an event' },
  ];

  const privacyOptions = [
    { id: 'public', label: 'Public', icon: GlobeAltIcon, description: 'Anyone can see' },
    { id: 'friends', label: 'Friends', icon: UserGroupIcon, description: 'Friends only' },
    { id: 'private', label: 'Private', icon: LockClosedIcon, description: 'Only you' },
  ];

  const handleMediaUpload = (event, type) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    // Validate file types and sizes
    const validFiles = files.filter(file => {
      if (type === 'image') {
        return file.type.startsWith('image/') && file.size <= 10 * 1024 * 1024; // 10MB
      } else if (type === 'video') {
        return file.type.startsWith('video/') && file.size <= 100 * 1024 * 1024; // 100MB
      }
      return false;
    });

    if (validFiles.length !== files.length) {
      toast.error('Some files were invalid or too large');
    }

    setMediaFiles(prev => [...prev, ...validFiles]);

    // Create preview URLs
    const previews = validFiles.map(file => ({
      file,
      url: URL.createObjectURL(file),
      type: file.type.startsWith('image/') ? 'image' : 'video'
    }));

    setMediaPreview(prev => [...prev, ...previews]);
    setPostType(type);
  };

  const removeMedia = (index) => {
    setMediaFiles(prev => prev.filter((_, i) => i !== index));
    setMediaPreview(prev => {
      const newPreviews = prev.filter((_, i) => i !== index);
      // Revoke URL to prevent memory leaks
      URL.revokeObjectURL(prev[index].url);
      return newPreviews;
    });

    if (mediaFiles.length === 1) {
      setPostType('text');
    }
  };

  const addPollOption = () => {
    if (pollOptions.length < 6) {
      setPollOptions(prev => [...prev, '']);
    }
  };

  const removePollOption = (index) => {
    if (pollOptions.length > 2) {
      setPollOptions(prev => prev.filter((_, i) => i !== index));
    }
  };

  const updatePollOption = (index, value) => {
    setPollOptions(prev => prev.map((option, i) => i === index ? value : option));
  };

  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim()) && tags.length < 10) {
      setTags(prev => [...prev, tagInput.trim()]);
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove) => {
    setTags(prev => prev.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (tagInput.trim()) {
        addTag();
      }
    }
  };

  const validatePost = () => {
    if (!content.trim() && mediaFiles.length === 0 && postType !== 'poll' && postType !== 'event') {
      toast.error('Please add some content to your post');
      return false;
    }

    if (postType === 'poll') {
      const validOptions = pollOptions.filter(option => option.trim());
      if (validOptions.length < 2) {
        toast.error('Poll must have at least 2 options');
        return false;
      }
    }

    if (postType === 'event') {
      if (!eventTitle.trim() || !eventDate || !eventTime) {
        toast.error('Please fill in all event details');
        return false;
      }
    }

    if (isScheduled && (!scheduledDate || !scheduledTime)) {
      toast.error('Please set a valid schedule date and time');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validatePost()) return;

    try {
      const postData = {
        content: content.trim(),
        type: postType,
        privacy,
        location: location.trim(),
        tags,
        community: community?._id,
      };

      // Add type-specific data
      if (postType === 'poll') {
        postData.poll = {
          options: pollOptions.filter(option => option.trim()),
          duration: parseInt(pollDuration),
          allowMultipleChoices,
        };
      }

      if (postType === 'event') {
        postData.event = {
          title: eventTitle.trim(),
          date: eventDate,
          time: eventTime,
          location: eventLocation.trim(),
        };
      }

      if (isScheduled) {
        postData.scheduledFor = new Date(`${scheduledDate}T${scheduledTime}`).toISOString();
      }

      // Handle media upload
      if (mediaFiles.length > 0) {
        const formData = new FormData();
        mediaFiles.forEach((file, index) => {
          formData.append(`media_${index}`, file);
        });
        // Add other post data to formData
        Object.keys(postData).forEach(key => {
          if (typeof postData[key] === 'object') {
            formData.append(key, JSON.stringify(postData[key]));
          } else {
            formData.append(key, postData[key]);
          }
        });
        
        await dispatch(createPost(formData));
      } else {
        await dispatch(createPost(postData));
      }

      toast.success(isScheduled ? 'Post scheduled successfully!' : 'Post created successfully!');
      onClose?.();
      
      // Reset form
      setContent('');
      setPostType('text');
      setMediaFiles([]);
      setMediaPreview([]);
      setPollOptions(['', '']);
      setEventTitle('');
      setEventDate('');
      setEventTime('');
      setTags([]);
      setLocation('');
      setIsScheduled(false);
      
    } catch (error) {
      toast.error('Failed to create post. Please try again.');
      console.error('Post creation error:', error);
    }
  };

  const getCharacterCount = () => {
    const maxLength = 2000;
    return `${content.length}/${maxLength}`;
  };

  const isOverLimit = () => content.length > 2000;

  const handleAIContentGenerated = (aiContent) => {
    // Extract just the text content from AI response, removing formatting
    const cleanContent = aiContent.replace(/[#*_`]/g, '').replace(/\n\n+/g, '\n\n');
    setContent(prev => prev + (prev ? '\n\n' : '') + cleanContent);
    setShowAIAssistant(false);
  };

  const handleMediaSelected = (selectedMedia) => {
    if (Array.isArray(selectedMedia)) {
      // Multiple files selected
      const newFiles = selectedMedia.map(media => ({
        file: null, // Mock file object
        url: media.url,
        type: media.type === 'image' ? 'image' : 'video',
        name: media.name
      }));
      setMediaPreview(prev => [...prev, ...newFiles]);
      setPostType(selectedMedia[0].type === 'image' ? 'image' : 'video');
    } else {
      // Single file selected
      const newFile = {
        file: null, // Mock file object
        url: selectedMedia.url,
        type: selectedMedia.type === 'image' ? 'image' : 'video',
        name: selectedMedia.name
      };
      setMediaPreview(prev => [...prev, newFile]);
      setPostType(selectedMedia.type === 'image' ? 'image' : 'video');
    }
    setShowMediaLibrary(false);
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="bg-background rounded-lg shadow-xl border border-gray-800 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800">
        <h2 className="text-xl font-semibold text-white">
          {isScheduled ? 'Schedule Post' : 'Create Post'}
        </h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white transition-colors"
        >
          <XMarkIcon className="h-6 w-6" />
        </button>
      </div>

      {/* Content */}
      <div className="p-4 space-y-4">
        {/* User info */}
        <div className="flex items-center space-x-3">
          <img
            src={user?.avatar || '/default-avatar.png'}
            alt={user?.username}
            className="w-10 h-10 rounded-full"
          />
          <div>
            <p className="font-medium text-white">{user?.realName}</p>
            <p className="text-sm text-gray-400">@{user?.username}</p>
          </div>
        </div>

        {/* Post type selector */}
        <div className="flex space-x-2 overflow-x-auto pb-2">
          {postTypes.map((type) => (
            <button
              key={type.id}
              onClick={() => setPostType(type.id)}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg whitespace-nowrap transition-colors ${
                postType === type.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              <type.icon className="h-4 w-4" />
              <span className="text-sm">{type.label}</span>
            </button>
          ))}
        </div>

        {/* Main content area */}
        <div className="space-y-4">
          {/* Text content */}
          <div className="relative">
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder={
                postType === 'poll' ? 'Ask a question...' :
                postType === 'event' ? 'Tell people about your event...' :
                "What's on your mind?"
              }
              className={`w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                isOverLimit() ? 'border-red-500' : ''
              }`}
              rows={4}
              maxLength={2000}
            />
            <div className={`absolute bottom-2 right-2 text-xs ${
              isOverLimit() ? 'text-red-400' : 'text-gray-500'
            }`}>
              {getCharacterCount()}
            </div>
          </div>

          {/* Media preview */}
          {mediaPreview.length > 0 && (
            <div className="grid grid-cols-2 gap-2">
              {mediaPreview.map((media, index) => (
                <div key={index} className="relative group">
                  {media.type === 'image' ? (
                    <img
                      src={media.url}
                      alt="Preview"
                      className="w-full h-32 object-cover rounded-lg"
                    />
                  ) : (
                    <video
                      src={media.url}
                      className="w-full h-32 object-cover rounded-lg"
                      controls
                    />
                  )}
                  <button
                    onClick={() => removeMedia(index)}
                    className="absolute top-2 right-2 bg-black/50 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Poll options */}
          {postType === 'poll' && (
            <div className="space-y-3">
              <h4 className="font-medium text-white">Poll Options</h4>
              {pollOptions.map((option, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={option}
                    onChange={(e) => updatePollOption(index, e.target.value)}
                    placeholder={`Option ${index + 1}`}
                    className="flex-1 p-2 bg-gray-800 border border-gray-700 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    maxLength={100}
                  />
                  {pollOptions.length > 2 && (
                    <button
                      onClick={() => removePollOption(index)}
                      className="text-red-400 hover:text-red-300"
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </button>
                  )}
                </div>
              ))}

              {pollOptions.length < 6 && (
                <button
                  onClick={addPollOption}
                  className="text-primary-400 hover:text-primary-300 text-sm"
                >
                  + Add option
                </button>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={allowMultipleChoices}
                      onChange={(e) => setAllowMultipleChoices(e.target.checked)}
                      className="rounded border-gray-700 bg-gray-800 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="text-sm text-gray-300">Allow multiple choices</span>
                  </label>
                </div>

                <select
                  value={pollDuration}
                  onChange={(e) => setPollDuration(e.target.value)}
                  className="bg-gray-800 border border-gray-700 rounded text-white text-sm p-1 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="1">1 hour</option>
                  <option value="6">6 hours</option>
                  <option value="24">1 day</option>
                  <option value="72">3 days</option>
                  <option value="168">1 week</option>
                </select>
              </div>
            </div>
          )}

          {/* Event details */}
          {postType === 'event' && (
            <div className="space-y-3">
              <h4 className="font-medium text-white">Event Details</h4>

              <input
                type="text"
                value={eventTitle}
                onChange={(e) => setEventTitle(e.target.value)}
                placeholder="Event title"
                className="w-full p-2 bg-gray-800 border border-gray-700 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
                maxLength={100}
              />

              <div className="grid grid-cols-2 gap-2">
                <input
                  type="date"
                  value={eventDate}
                  onChange={(e) => setEventDate(e.target.value)}
                  className="p-2 bg-gray-800 border border-gray-700 rounded text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
                <input
                  type="time"
                  value={eventTime}
                  onChange={(e) => setEventTime(e.target.value)}
                  className="p-2 bg-gray-800 border border-gray-700 rounded text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              <input
                type="text"
                value={eventLocation}
                onChange={(e) => setEventLocation(e.target.value)}
                placeholder="Event location (optional)"
                className="w-full p-2 bg-gray-800 border border-gray-700 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
                maxLength={200}
              />
            </div>
          )}

          {/* Tags */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Add tags..."
                className="flex-1 p-2 bg-gray-800 border border-gray-700 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
                maxLength={30}
              />
              <button
                onClick={addTag}
                disabled={!tagInput.trim() || tags.length >= 10}
                className="px-3 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add
              </button>
            </div>

            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center space-x-1 bg-primary-600/20 text-primary-400 px-2 py-1 rounded-full text-sm"
                  >
                    <span>#{tag}</span>
                    <button
                      onClick={() => removeTag(tag)}
                      className="text-primary-400 hover:text-primary-300"
                    >
                      <XMarkIcon className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* Location */}
          <div className="flex items-center space-x-2">
            <MapPinIcon className="h-5 w-5 text-gray-400" />
            <input
              type="text"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              placeholder="Add location..."
              className="flex-1 p-2 bg-gray-800 border border-gray-700 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
              maxLength={100}
            />
          </div>
        </div>

        {/* Privacy and scheduling */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-800">
          <div className="flex items-center space-x-4">
            {/* Privacy selector */}
            <select
              value={privacy}
              onChange={(e) => setPrivacy(e.target.value)}
              className="bg-gray-800 border border-gray-700 rounded text-white text-sm p-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              {privacyOptions.map((option) => (
                <option key={option.id} value={option.id}>
                  {option.label}
                </option>
              ))}
            </select>

            {/* Schedule toggle */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={isScheduled}
                onChange={(e) => setIsScheduled(e.target.checked)}
                className="rounded border-gray-700 bg-gray-800 text-primary-600 focus:ring-primary-500"
              />
              <span className="text-sm text-gray-300">Schedule</span>
            </label>
          </div>

          {/* Media upload buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowAIAssistant(true)}
              className="p-2 text-gray-400 hover:text-purple-400 transition-colors"
              title="AI Assistant"
            >
              <SparklesIcon className="h-5 w-5" />
            </button>

            <button
              onClick={() => setShowMediaLibrary(true)}
              className="p-2 text-gray-400 hover:text-primary-400 transition-colors"
              title="Add media from library"
            >
              <PhotoIcon className="h-5 w-5" />
            </button>

            <button
              onClick={() => fileInputRef.current?.click()}
              className="p-2 text-gray-400 hover:text-primary-400 transition-colors"
              title="Upload new media"
            >
              <VideoCameraIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Schedule date/time */}
        {isScheduled && (
          <div className="grid grid-cols-2 gap-2 pt-2">
            <input
              type="date"
              value={scheduledDate}
              onChange={(e) => setScheduledDate(e.target.value)}
              min={new Date().toISOString().split('T')[0]}
              className="p-2 bg-gray-800 border border-gray-700 rounded text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
            <input
              type="time"
              value={scheduledTime}
              onChange={(e) => setScheduledTime(e.target.value)}
              className="p-2 bg-gray-800 border border-gray-700 rounded text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between p-4 border-t border-gray-800">
        <div className="text-sm text-gray-400">
          {community && (
            <span>Posting to <strong>{community.name}</strong></span>
          )}
        </div>

        <div className="flex items-center space-x-3">
          <Button
            variant="secondary"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading || isOverLimit()}
            loading={isLoading}
          >
            {isScheduled ? 'Schedule' : 'Post'}
          </Button>
        </div>
      </div>
      {/* AI Assistant Modal */}
      <AIAssistant
        isOpen={showAIAssistant}
        onClose={() => setShowAIAssistant(false)}
        onContentGenerated={handleAIContentGenerated}
        context={{ postType, content }}
      />

      {/* Media Library Modal */}
      <MediaLibrary
        isOpen={showMediaLibrary}
        onClose={() => setShowMediaLibrary(false)}
        onSelectMedia={handleMediaSelected}
        allowMultiple={true}
      />
    </motion.div>
  );
};

export default CreatePost;
