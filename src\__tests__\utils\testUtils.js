import React from 'react';
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';

// Import reducers
import authReducer from '../../store/slices/authSlice';
import communityReducer from '../../store/slices/communitySlice';
import postReducer from '../../store/slices/postSlice';
import feedReducer from '../../store/slices/feedSlice';
import notificationReducer from '../../store/slices/notificationSlice';
import chatReducer from '../../store/slices/chatSlice';
import profileReducer from '../../store/slices/profileSlice';
import exploreReducer from '../../store/slices/exploreSlice';
import uiReducer from '../../store/slices/uiSlice';
import categoryReducer from '../../store/slices/categorySlice';
import featureReducer from '../../store/slices/featureSlice';

// Default initial state for tests
const defaultInitialState = {
  auth: {
    user: {
      id: '1',
      username: 'testuser',
      email: '<EMAIL>',
      name: 'Test User',
    },
    token: 'test-token',
    isAuthenticated: true,
    isLoading: false,
    error: null,
    message: null,
  },
  notifications: {
    notifications: [],
    unreadCount: 0,
    loading: false,
    error: null,
  },
  chat: {
    chats: [],
    activeChat: null,
    unreadCounts: {},
    isLoading: false,
    error: null,
  },
  communities: {
    communities: [],
    currentCommunity: null,
    isLoading: false,
    error: null,
  },
  posts: {
    posts: [],
    isLoading: false,
    error: null,
  },
  feed: {
    posts: [],
    isLoading: false,
    hasMore: true,
    error: null,
  },
  profile: {
    currentProfile: null,
    viewedProfile: null,
    isLoading: false,
    error: null,
  },
  explore: {
    trendingTopics: [],
    suggestedPeople: [],
    isLoading: false,
    error: null,
  },
  ui: {
    sidebarCollapsed: false,
    theme: 'light',
  },
  categories: {
    categories: [],
    isLoading: false,
    error: null,
  },
  features: {
    features: [],
    isLoading: false,
    error: null,
  },
};

// Create a test store
export function createTestStore(initialState = {}) {
  const mergedState = {
    ...defaultInitialState,
    ...initialState,
  };

  return configureStore({
    reducer: {
      auth: authReducer,
      communities: communityReducer,
      posts: postReducer,
      feed: feedReducer,
      notifications: notificationReducer,
      chat: chatReducer,
      profile: profileReducer,
      explore: exploreReducer,
      ui: uiReducer,
      categories: categoryReducer,
      features: featureReducer,
    },
    preloadedState: mergedState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  });
}

// Custom render function that includes providers
export function renderWithProviders(
  ui,
  {
    initialState = {},
    store = createTestStore(initialState),
    route = '/',
    ...renderOptions
  } = {}
) {
  // Set up router history
  window.history.pushState({}, 'Test page', route);

  function Wrapper({ children }) {
    return (
      <Provider store={store}>
        <BrowserRouter>
          {children}
        </BrowserRouter>
      </Provider>
    );
  }

  return {
    store,
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  };
}

// Mock user data
export const mockUser = {
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  name: 'Test User',
  avatar: null,
  bio: 'Test user bio',
  followers: 100,
  following: 50,
};

// Mock notification data
export const mockNotifications = [
  {
    id: '1',
    type: 'like',
    message: 'John Doe liked your post',
    read: false,
    createdAt: '2024-01-15T10:30:00Z',
    link: '/posts/1',
  },
  {
    id: '2',
    type: 'comment',
    message: 'Jane Smith commented on your post',
    read: true,
    createdAt: '2024-01-14T15:45:00Z',
    link: '/posts/2',
  },
];

// Mock chat data
export const mockChats = [
  {
    id: '1',
    name: 'John Doe',
    participants: [{ username: 'johndoe', name: 'John Doe' }],
    lastMessage: 'Hello there!',
    updatedAt: '2024-01-15T10:30:00Z',
  },
];

// Mock search results
export const mockSearchResults = {
  users: [
    {
      id: '1',
      username: 'johndoe',
      name: 'John Doe',
      avatar: null,
      bio: 'Software developer',
      followers: 150,
      isFollowing: false,
    },
  ],
  posts: [
    {
      id: '1',
      content: 'This is a test post',
      author: { username: 'johndoe', name: 'John Doe' },
      createdAt: '2024-01-15T10:30:00Z',
      likes: 10,
      comments: 5,
    },
  ],
  communities: [
    {
      id: '1',
      name: 'Tech Community',
      slug: 'tech-community',
      description: 'A community for tech enthusiasts',
      members: 1000,
      isJoined: false,
    },
  ],
  hashtags: [
    { tag: 'technology', count: 500 },
    { tag: 'programming', count: 300 },
  ],
};

// Re-export everything from React Testing Library
export * from '@testing-library/react';
export { renderWithProviders as render };
