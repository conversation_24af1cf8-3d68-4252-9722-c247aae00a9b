/**
 * PWA (Progressive Web App) Utilities
 * Handles service worker registration, installation prompts, and offline functionality
 */

// Service Worker registration
export const registerServiceWorker = async () => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      console.log('Service Worker registered successfully:', registration);

      // Handle updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // New content is available, show update notification
            showUpdateNotification();
          }
        });
      });

      return registration;
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      return null;
    }
  } else {
    console.log('Service Worker not supported');
    return null;
  }
};

// Show update notification
const showUpdateNotification = () => {
  if (window.confirm('A new version of NeTuArk is available. Would you like to update?')) {
    window.location.reload();
  }
};

// Install prompt handling
let deferredPrompt = null;

export const initializeInstallPrompt = () => {
  window.addEventListener('beforeinstallprompt', (event) => {
    console.log('Install prompt available');
    
    // Prevent the mini-infobar from appearing on mobile
    event.preventDefault();
    
    // Stash the event so it can be triggered later
    deferredPrompt = event;
    
    // Show custom install button
    showInstallButton();
  });

  window.addEventListener('appinstalled', () => {
    console.log('PWA was installed');
    hideInstallButton();
    deferredPrompt = null;
    
    // Track installation
    if (window.gtag) {
      window.gtag('event', 'pwa_install', {
        event_category: 'engagement',
        event_label: 'PWA Installation'
      });
    }
  });
};

// Show install button
const showInstallButton = () => {
  const installButton = document.getElementById('pwa-install-button');
  if (installButton) {
    installButton.style.display = 'block';
  }
  
  // Dispatch custom event for React components
  window.dispatchEvent(new CustomEvent('pwa-install-available'));
};

// Hide install button
const hideInstallButton = () => {
  const installButton = document.getElementById('pwa-install-button');
  if (installButton) {
    installButton.style.display = 'none';
  }
  
  // Dispatch custom event for React components
  window.dispatchEvent(new CustomEvent('pwa-install-completed'));
};

// Trigger install prompt
export const showInstallPrompt = async () => {
  if (!deferredPrompt) {
    console.log('Install prompt not available');
    return false;
  }

  try {
    // Show the install prompt
    deferredPrompt.prompt();
    
    // Wait for the user to respond to the prompt
    const { outcome } = await deferredPrompt.userChoice;
    
    console.log(`User response to install prompt: ${outcome}`);
    
    // Track user choice
    if (window.gtag) {
      window.gtag('event', 'pwa_install_prompt', {
        event_category: 'engagement',
        event_label: outcome
      });
    }
    
    // Clear the deferredPrompt
    deferredPrompt = null;
    
    return outcome === 'accepted';
  } catch (error) {
    console.error('Error showing install prompt:', error);
    return false;
  }
};

// Check if app is installed
export const isAppInstalled = () => {
  return window.matchMedia('(display-mode: standalone)').matches ||
         window.navigator.standalone === true;
};

// Network status monitoring
export const initializeNetworkMonitoring = () => {
  const updateNetworkStatus = () => {
    const isOnline = navigator.onLine;
    
    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('network-status-change', {
      detail: { isOnline }
    }));
    
    // Update UI
    if (isOnline) {
      console.log('App is online');
      hideOfflineNotification();
    } else {
      console.log('App is offline');
      showOfflineNotification();
    }
  };

  window.addEventListener('online', updateNetworkStatus);
  window.addEventListener('offline', updateNetworkStatus);
  
  // Initial check
  updateNetworkStatus();
};

// Show offline notification
const showOfflineNotification = () => {
  const notification = document.createElement('div');
  notification.id = 'offline-notification';
  notification.innerHTML = `
    <div style="
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: #ef4444;
      color: white;
      padding: 0.75rem;
      text-align: center;
      z-index: 9999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    ">
      📱 You're offline. Some features may not be available.
    </div>
  `;
  
  // Remove existing notification
  const existing = document.getElementById('offline-notification');
  if (existing) {
    existing.remove();
  }
  
  document.body.appendChild(notification);
};

// Hide offline notification
const hideOfflineNotification = () => {
  const notification = document.getElementById('offline-notification');
  if (notification) {
    notification.remove();
  }
};

// Background sync for offline actions
export const scheduleBackgroundSync = (tag) => {
  if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
    navigator.serviceWorker.ready.then((registration) => {
      return registration.sync.register(tag);
    }).catch((error) => {
      console.error('Background sync registration failed:', error);
    });
  }
};

// Push notification subscription
export const subscribeToPushNotifications = async () => {
  if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
    console.log('Push notifications not supported');
    return null;
  }

  try {
    const registration = await navigator.serviceWorker.ready;
    
    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array(
        process.env.VITE_VAPID_PUBLIC_KEY || 'your-vapid-public-key'
      )
    });

    console.log('Push notification subscription:', subscription);
    
    // Send subscription to server
    await sendSubscriptionToServer(subscription);
    
    return subscription;
  } catch (error) {
    console.error('Push notification subscription failed:', error);
    return null;
  }
};

// Helper function to convert VAPID key
const urlBase64ToUint8Array = (base64String) => {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
};

// Send subscription to server
const sendSubscriptionToServer = async (subscription) => {
  try {
    const response = await fetch('/api/push-subscription', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(subscription)
    });

    if (!response.ok) {
      throw new Error('Failed to send subscription to server');
    }

    console.log('Subscription sent to server successfully');
  } catch (error) {
    console.error('Failed to send subscription to server:', error);
  }
};

// Cache management
export const clearAppCache = async () => {
  if ('caches' in window) {
    try {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
      console.log('App cache cleared');
      return true;
    } catch (error) {
      console.error('Failed to clear cache:', error);
      return false;
    }
  }
  return false;
};

// Get cache size
export const getCacheSize = async () => {
  if ('caches' in window && 'storage' in navigator && 'estimate' in navigator.storage) {
    try {
      const estimate = await navigator.storage.estimate();
      return {
        used: estimate.usage,
        available: estimate.quota,
        percentage: Math.round((estimate.usage / estimate.quota) * 100)
      };
    } catch (error) {
      console.error('Failed to get cache size:', error);
      return null;
    }
  }
  return null;
};

// Initialize all PWA features
export const initializePWA = () => {
  console.log('Initializing PWA features...');
  
  registerServiceWorker();
  initializeInstallPrompt();
  initializeNetworkMonitoring();
  
  console.log('PWA features initialized');
};
