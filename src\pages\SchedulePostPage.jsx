import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  CalendarIcon,
  ClockIcon,
  TrashIcon,
  PencilIcon,
  EyeIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';

const SchedulePostPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const draftId = searchParams.get('draft');
  
  const [scheduledPosts, setScheduledPosts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPosts, setSelectedPosts] = useState(new Set());
  const [showScheduleForm, setShowScheduleForm] = useState(false);
  const [scheduleForm, setScheduleForm] = useState({
    postId: '',
    title: '',
    content: '',
    scheduledDate: '',
    scheduledTime: '',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    community: '',
    tags: [],
  });

  // Load scheduled posts from localStorage (mock data)
  useEffect(() => {
    const loadScheduledPosts = () => {
      const savedPosts = localStorage.getItem('scheduledPosts');
      if (savedPosts) {
        setScheduledPosts(JSON.parse(savedPosts));
      } else {
        // Mock scheduled posts data
        const mockScheduledPosts = [
          {
            id: '1',
            title: 'Weekly Tech Update',
            content: 'Here are the latest tech updates for this week...',
            scheduledAt: '2024-01-20T09:00:00Z',
            createdAt: '2024-01-15T10:30:00Z',
            status: 'scheduled',
            community: 'tech-enthusiasts',
            tags: ['tech', 'weekly', 'update'],
            wordCount: 245,
          },
          {
            id: '2',
            title: 'Design System Launch',
            content: 'Excited to announce the launch of our new design system...',
            scheduledAt: '2024-01-22T14:30:00Z',
            createdAt: '2024-01-16T11:15:00Z',
            status: 'scheduled',
            community: 'design-community',
            tags: ['design', 'system', 'launch'],
            wordCount: 189,
          },
          {
            id: '3',
            title: 'Monthly Reflection',
            content: 'Looking back at this month\'s achievements and learnings...',
            scheduledAt: '2024-01-31T18:00:00Z',
            createdAt: '2024-01-14T16:45:00Z',
            status: 'scheduled',
            community: null,
            tags: ['reflection', 'monthly', 'personal'],
            wordCount: 156,
          },
        ];
        setScheduledPosts(mockScheduledPosts);
        localStorage.setItem('scheduledPosts', JSON.stringify(mockScheduledPosts));
      }
      setIsLoading(false);
    };

    loadScheduledPosts();
  }, []);

  // If coming from draft, pre-fill the form
  useEffect(() => {
    if (draftId) {
      const drafts = JSON.parse(localStorage.getItem('draftPosts') || '[]');
      const draft = drafts.find(d => d.id === draftId);
      if (draft) {
        setScheduleForm({
          postId: draft.id,
          title: draft.title || '',
          content: draft.content || '',
          scheduledDate: '',
          scheduledTime: '',
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          community: draft.community || '',
          tags: draft.tags || [],
        });
        setShowScheduleForm(true);
      }
    }
  }, [draftId]);

  // Save scheduled posts to localStorage
  const saveScheduledPosts = (updatedPosts) => {
    setScheduledPosts(updatedPosts);
    localStorage.setItem('scheduledPosts', JSON.stringify(updatedPosts));
  };

  // Handle form input changes
  const handleFormChange = (field, value) => {
    setScheduleForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Schedule a post
  const schedulePost = (e) => {
    e.preventDefault();
    
    if (!scheduleForm.scheduledDate || !scheduleForm.scheduledTime) {
      alert('Please select both date and time');
      return;
    }

    const scheduledDateTime = new Date(`${scheduleForm.scheduledDate}T${scheduleForm.scheduledTime}`);
    
    if (scheduledDateTime <= new Date()) {
      alert('Please select a future date and time');
      return;
    }

    const newScheduledPost = {
      id: Date.now().toString(),
      title: scheduleForm.title,
      content: scheduleForm.content,
      scheduledAt: scheduledDateTime.toISOString(),
      createdAt: new Date().toISOString(),
      status: 'scheduled',
      community: scheduleForm.community,
      tags: scheduleForm.tags,
      wordCount: scheduleForm.content.split(' ').length,
    };

    const updatedPosts = [...scheduledPosts, newScheduledPost];
    saveScheduledPosts(updatedPosts);

    // Remove from drafts if it was a draft
    if (scheduleForm.postId) {
      const drafts = JSON.parse(localStorage.getItem('draftPosts') || '[]');
      const updatedDrafts = drafts.filter(d => d.id !== scheduleForm.postId);
      localStorage.setItem('draftPosts', JSON.stringify(updatedDrafts));
    }

    // Reset form
    setScheduleForm({
      postId: '',
      title: '',
      content: '',
      scheduledDate: '',
      scheduledTime: '',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      community: '',
      tags: [],
    });
    setShowScheduleForm(false);
    
    alert('Post scheduled successfully!');
  };

  // Delete scheduled post
  const deleteScheduledPost = (postId) => {
    const updatedPosts = scheduledPosts.filter(post => post.id !== postId);
    saveScheduledPosts(updatedPosts);
  };

  // Publish scheduled post immediately
  const publishNow = (postId) => {
    // In a real app, this would make an API call to publish the post
    console.log('Publishing scheduled post:', postId);
    deleteScheduledPost(postId);
    alert('Post published successfully!');
  };

  // Toggle post selection
  const togglePostSelection = (postId) => {
    const newSelected = new Set(selectedPosts);
    if (newSelected.has(postId)) {
      newSelected.delete(postId);
    } else {
      newSelected.add(postId);
    }
    setSelectedPosts(newSelected);
  };

  // Delete selected posts
  const deleteSelectedPosts = () => {
    const updatedPosts = scheduledPosts.filter(post => !selectedPosts.has(post.id));
    saveScheduledPosts(updatedPosts);
    setSelectedPosts(new Set());
  };

  // Format date and time
  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
      }),
    };
  };

  // Get time until scheduled
  const getTimeUntilScheduled = (scheduledAt) => {
    const now = new Date();
    const scheduled = new Date(scheduledAt);
    const diffInMs = scheduled - now;
    
    if (diffInMs <= 0) return 'Overdue';
    
    const days = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diffInMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diffInMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `in ${days}d ${hours}h`;
    if (hours > 0) return `in ${hours}h ${minutes}m`;
    return `in ${minutes}m`;
  };

  // Get minimum date (today)
  const getMinDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  // Get minimum time (if today is selected)
  const getMinTime = () => {
    if (scheduleForm.scheduledDate === getMinDate()) {
      const now = new Date();
      return now.toTimeString().slice(0, 5);
    }
    return '';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="max-w-4xl mx-auto p-4 pb-20"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <CalendarIcon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Scheduled Posts
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {scheduledPosts.length} scheduled post{scheduledPosts.length !== 1 ? 's' : ''}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {selectedPosts.size > 0 && (
            <button
              onClick={deleteSelectedPosts}
              className="px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 border border-red-300 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
            >
              Delete Selected ({selectedPosts.size})
            </button>
          )}
          
          <button
            onClick={() => setShowScheduleForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            <PlusIcon className="h-4 w-4" />
            <span>Schedule Post</span>
          </button>
        </div>
      </div>

      {/* Schedule Form Modal */}
      {showScheduleForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white dark:bg-dark-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                Schedule Post
              </h2>
              <button
                onClick={() => setShowScheduleForm(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                ×
              </button>
            </div>

            <form onSubmit={schedulePost} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Title
                </label>
                <input
                  type="text"
                  value={scheduleForm.title}
                  onChange={(e) => handleFormChange('title', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Enter post title..."
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Content
                </label>
                <textarea
                  value={scheduleForm.content}
                  onChange={(e) => handleFormChange('content', e.target.value)}
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="What's on your mind?"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Date
                  </label>
                  <input
                    type="date"
                    value={scheduleForm.scheduledDate}
                    onChange={(e) => handleFormChange('scheduledDate', e.target.value)}
                    min={getMinDate()}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Time
                  </label>
                  <input
                    type="time"
                    value={scheduleForm.scheduledTime}
                    onChange={(e) => handleFormChange('scheduledTime', e.target.value)}
                    min={getMinTime()}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Community (Optional)
                </label>
                <select
                  value={scheduleForm.community}
                  onChange={(e) => handleFormChange('community', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">Select a community...</option>
                  <option value="tech-enthusiasts">Tech Enthusiasts</option>
                  <option value="design-community">Design Community</option>
                  <option value="startup-founders">Startup Founders</option>
                </select>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowScheduleForm(false)}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-dark-600 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                >
                  Schedule Post
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="space-y-4">
          {[...Array(3)].map((_, index) => (
            <div
              key={index}
              className="bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700 animate-pulse"
            >
              <div className="flex items-start space-x-4">
                <div className="w-4 h-4 bg-gray-200 dark:bg-dark-700 rounded"></div>
                <div className="flex-1 space-y-3">
                  <div className="h-4 bg-gray-200 dark:bg-dark-700 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 dark:bg-dark-700 rounded w-full"></div>
                  <div className="h-3 bg-gray-200 dark:bg-dark-700 rounded w-2/3"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && scheduledPosts.length === 0 && (
        <div className="text-center py-12">
          <CalendarIcon className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No scheduled posts
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            Schedule your first post to see it here.
          </p>
          <button
            onClick={() => setShowScheduleForm(true)}
            className="inline-flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            <PlusIcon className="h-4 w-4" />
            <span>Schedule Your First Post</span>
          </button>
        </div>
      )}

      {/* Scheduled Posts List */}
      {!isLoading && scheduledPosts.length > 0 && (
        <div className="space-y-4">
          {scheduledPosts
            .sort((a, b) => new Date(a.scheduledAt) - new Date(b.scheduledAt))
            .map((post) => {
              const { date, time } = formatDateTime(post.scheduledAt);
              const timeUntil = getTimeUntilScheduled(post.scheduledAt);
              
              return (
                <motion.div
                  key={post.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white dark:bg-dark-800 rounded-lg p-6 border border-gray-200 dark:border-dark-700 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start space-x-4">
                    {/* Selection Checkbox */}
                    <input
                      type="checkbox"
                      checked={selectedPosts.has(post.id)}
                      onChange={() => togglePostSelection(post.id)}
                      className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />

                    {/* Post Content */}
                    <div className="flex-1 min-w-0">
                      {/* Title */}
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {post.title}
                      </h3>

                      {/* Content Preview */}
                      <p className="text-gray-600 dark:text-gray-300 mb-3 leading-relaxed">
                        {post.content.length > 150 
                          ? post.content.substring(0, 150) + '...'
                          : post.content
                        }
                      </p>

                      {/* Schedule Info */}
                      <div className="flex items-center space-x-4 mb-3">
                        <div className="flex items-center space-x-2 text-sm text-primary-600 dark:text-primary-400">
                          <CalendarIcon className="h-4 w-4" />
                          <span>{date}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-primary-600 dark:text-primary-400">
                          <ClockIcon className="h-4 w-4" />
                          <span>{time}</span>
                        </div>
                        <span className="text-sm font-medium text-green-600 dark:text-green-400">
                          {timeUntil}
                        </span>
                      </div>

                      {/* Tags and Community */}
                      <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                        <span>{post.wordCount} words</span>
                        {post.community && (
                          <>
                            <span>•</span>
                            <span className="text-primary-600 dark:text-primary-400">
                              {post.community}
                            </span>
                          </>
                        )}
                        {post.tags.length > 0 && (
                          <>
                            <span>•</span>
                            <span>{post.tags.map(tag => `#${tag}`).join(', ')}</span>
                          </>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          setScheduleForm({
                            postId: post.id,
                            title: post.title,
                            content: post.content,
                            scheduledDate: post.scheduledAt.split('T')[0],
                            scheduledTime: post.scheduledAt.split('T')[1].slice(0, 5),
                            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                            community: post.community || '',
                            tags: post.tags || [],
                          });
                          setShowScheduleForm(true);
                        }}
                        className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 rounded-lg transition-colors"
                        title="Edit scheduled post"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      
                      <button
                        onClick={() => publishNow(post.id)}
                        className="px-3 py-1 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                        title="Publish now"
                      >
                        Publish Now
                      </button>
                      
                      <button
                        onClick={() => deleteScheduledPost(post.id)}
                        className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                        title="Delete scheduled post"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              );
            })}
        </div>
      )}
    </motion.div>
  );
};

export default SchedulePostPage;
