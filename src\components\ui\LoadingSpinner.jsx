import { motion } from 'framer-motion';
import { ArrowPathIcon } from '@heroicons/react/24/outline';

const LoadingSpinner = ({ 
  size = 'md', 
  color = 'primary', 
  text = '', 
  fullScreen = false,
  overlay = false 
}) => {
  const sizeClasses = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  };

  const colorClasses = {
    primary: 'text-primary-500',
    white: 'text-white',
    gray: 'text-gray-400',
    blue: 'text-blue-500',
    green: 'text-green-500',
    red: 'text-red-500'
  };

  const textSizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  const SpinnerIcon = () => (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: "linear"
      }}
      className={`${sizeClasses[size]} ${colorClasses[color]}`}
    >
      <ArrowPathIcon />
    </motion.div>
  );

  const SpinnerContent = () => (
    <div className="flex flex-col items-center justify-center space-y-2">
      <SpinnerIcon />
      {text && (
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className={`${textSizeClasses[size]} ${colorClasses[color]} font-medium`}
        >
          {text}
        </motion.p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-background flex items-center justify-center z-50"
      >
        <SpinnerContent />
      </motion.div>
    );
  }

  if (overlay) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-black/50 flex items-center justify-center z-40 rounded-lg"
      >
        <SpinnerContent />
      </motion.div>
    );
  }

  return <SpinnerContent />;
};

// Skeleton loading components
export const SkeletonLoader = ({ 
  lines = 3, 
  className = '', 
  animate = true 
}) => {
  const skeletonVariants = {
    loading: {
      opacity: [0.5, 1, 0.5],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <motion.div
          key={index}
          variants={animate ? skeletonVariants : {}}
          animate={animate ? "loading" : ""}
          className={`h-4 bg-gray-700 rounded ${
            index === lines - 1 ? 'w-3/4' : 'w-full'
          }`}
        />
      ))}
    </div>
  );
};

export const PostSkeleton = () => (
  <div className="bg-background rounded-lg border border-gray-800 p-6 space-y-4">
    {/* Header */}
    <div className="flex items-center space-x-3">
      <motion.div
        animate={{ opacity: [0.5, 1, 0.5] }}
        transition={{ duration: 1.5, repeat: Infinity }}
        className="w-12 h-12 bg-gray-700 rounded-full"
      />
      <div className="flex-1 space-y-2">
        <motion.div
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 1.5, repeat: Infinity, delay: 0.1 }}
          className="h-4 bg-gray-700 rounded w-1/3"
        />
        <motion.div
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 1.5, repeat: Infinity, delay: 0.2 }}
          className="h-3 bg-gray-700 rounded w-1/4"
        />
      </div>
    </div>

    {/* Content */}
    <div className="space-y-2">
      <motion.div
        animate={{ opacity: [0.5, 1, 0.5] }}
        transition={{ duration: 1.5, repeat: Infinity, delay: 0.3 }}
        className="h-4 bg-gray-700 rounded w-full"
      />
      <motion.div
        animate={{ opacity: [0.5, 1, 0.5] }}
        transition={{ duration: 1.5, repeat: Infinity, delay: 0.4 }}
        className="h-4 bg-gray-700 rounded w-4/5"
      />
    </div>

    {/* Image placeholder */}
    <motion.div
      animate={{ opacity: [0.5, 1, 0.5] }}
      transition={{ duration: 1.5, repeat: Infinity, delay: 0.5 }}
      className="h-48 bg-gray-700 rounded-lg"
    />

    {/* Actions */}
    <div className="flex items-center justify-between pt-4 border-t border-gray-800">
      <div className="flex space-x-6">
        {[1, 2, 3].map((i) => (
          <motion.div
            key={i}
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity, delay: 0.6 + i * 0.1 }}
            className="h-5 w-12 bg-gray-700 rounded"
          />
        ))}
      </div>
      <motion.div
        animate={{ opacity: [0.5, 1, 0.5] }}
        transition={{ duration: 1.5, repeat: Infinity, delay: 0.9 }}
        className="h-5 w-5 bg-gray-700 rounded"
      />
    </div>
  </div>
);

export const CardSkeleton = ({ count = 1 }) => (
  <div className="space-y-4">
    {Array.from({ length: count }).map((_, index) => (
      <div key={index} className="bg-background rounded-lg border border-gray-800 p-4">
        <div className="flex items-center space-x-3 mb-4">
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity, delay: index * 0.1 }}
            className="w-10 h-10 bg-gray-700 rounded-full"
          />
          <div className="flex-1 space-y-2">
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: index * 0.1 + 0.1 }}
              className="h-4 bg-gray-700 rounded w-1/3"
            />
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: index * 0.1 + 0.2 }}
              className="h-3 bg-gray-700 rounded w-1/4"
            />
          </div>
        </div>
        <SkeletonLoader lines={2} />
      </div>
    ))}
  </div>
);

// Loading states for different components
export const FeedLoader = () => (
  <div className="space-y-6">
    {Array.from({ length: 3 }).map((_, index) => (
      <PostSkeleton key={index} />
    ))}
  </div>
);

export const ProfileLoader = () => (
  <div className="space-y-6">
    {/* Profile header skeleton */}
    <div className="bg-background rounded-lg border border-gray-800 p-6">
      <div className="flex items-center space-x-4 mb-4">
        <motion.div
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 1.5, repeat: Infinity }}
          className="w-20 h-20 bg-gray-700 rounded-full"
        />
        <div className="flex-1 space-y-2">
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity, delay: 0.1 }}
            className="h-6 bg-gray-700 rounded w-1/3"
          />
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity, delay: 0.2 }}
            className="h-4 bg-gray-700 rounded w-1/4"
          />
        </div>
      </div>
      <SkeletonLoader lines={2} />
    </div>

    {/* Posts skeleton */}
    <FeedLoader />
  </div>
);

export const CommunitiesLoader = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {Array.from({ length: 6 }).map((_, index) => (
      <div key={index} className="bg-background rounded-lg border border-gray-800 overflow-hidden">
        <motion.div
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 1.5, repeat: Infinity, delay: index * 0.1 }}
          className="h-32 bg-gray-700"
        />
        <div className="p-4 space-y-3">
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity, delay: index * 0.1 + 0.1 }}
            className="h-5 bg-gray-700 rounded w-2/3"
          />
          <SkeletonLoader lines={2} />
          <div className="flex items-center justify-between">
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: index * 0.1 + 0.2 }}
              className="h-4 bg-gray-700 rounded w-1/4"
            />
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: index * 0.1 + 0.3 }}
              className="h-8 bg-gray-700 rounded w-16"
            />
          </div>
        </div>
      </div>
    ))}
  </div>
);

export default LoadingSpinner;
