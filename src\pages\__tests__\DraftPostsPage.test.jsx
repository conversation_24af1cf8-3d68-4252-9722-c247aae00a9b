import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from '../../__tests__/utils/testUtils';
import DraftPostsPage from '../DraftPostsPage';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock navigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock window.alert
global.alert = jest.fn();

const mockDrafts = [
  {
    id: '1',
    content: 'This is my first draft post about technology trends in 2024...',
    title: 'Tech Trends 2024',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T14:45:00Z',
    wordCount: 156,
    tags: ['technology', 'trends', '2024'],
    community: null,
  },
  {
    id: '2',
    content: 'Working on a new design system for our company...',
    title: 'Design System Thoughts',
    createdAt: '2024-01-14T09:15:00Z',
    updatedAt: '2024-01-14T16:20:00Z',
    wordCount: 89,
    tags: ['design', 'system', 'components'],
    community: 'design-community',
  },
];

describe('DraftPostsPage', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    mockNavigate.mockClear();
    global.alert.mockClear();
  });

  it('renders page header correctly', () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    expect(screen.getByText('Draft Posts')).toBeInTheDocument();
    expect(screen.getByText('2 drafts')).toBeInTheDocument();
  });

  it('shows singular draft count', () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify([mockDrafts[0]]));
    renderWithProviders(<DraftPostsPage />);

    expect(screen.getByText('1 draft')).toBeInTheDocument();
  });

  it('renders new draft button', () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    expect(screen.getByText('New Draft')).toBeInTheDocument();
  });

  it('navigates to create post when new draft clicked', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    const newDraftButton = screen.getByText('New Draft');
    await user.click(newDraftButton);

    expect(mockNavigate).toHaveBeenCalledWith('/create-post');
  });

  it('shows loading state initially', () => {
    localStorageMock.getItem.mockReturnValue(null);
    renderWithProviders(<DraftPostsPage />);

    const loadingElements = document.querySelectorAll('.animate-pulse');
    expect(loadingElements.length).toBeGreaterThan(0);
  });

  it('shows empty state when no drafts', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify([]));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(() => {
      expect(screen.getByText('No drafts yet')).toBeInTheDocument();
      expect(screen.getByText('Start writing your first draft post to see it here.')).toBeInTheDocument();
    });
  });

  it('renders draft list when drafts exist', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(() => {
      expect(screen.getByText('Tech Trends 2024')).toBeInTheDocument();
      expect(screen.getByText('Design System Thoughts')).toBeInTheDocument();
    });
  });

  it('displays draft content preview', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(() => {
      expect(screen.getByText(/This is my first draft post about technology trends/)).toBeInTheDocument();
      expect(screen.getByText(/Working on a new design system/)).toBeInTheDocument();
    });
  });

  it('displays draft metadata', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(() => {
      expect(screen.getByText('156 words')).toBeInTheDocument();
      expect(screen.getByText('89 words')).toBeInTheDocument();
    });
  });

  it('displays tags', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(() => {
      expect(screen.getByText('#technology')).toBeInTheDocument();
      expect(screen.getByText('#trends')).toBeInTheDocument();
      expect(screen.getByText('#design')).toBeInTheDocument();
    });
  });

  it('displays community when present', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(() => {
      expect(screen.getByText('design-community')).toBeInTheDocument();
    });
  });

  it('handles draft selection', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(async () => {
      const checkboxes = screen.getAllByRole('checkbox');
      await user.click(checkboxes[1]); // Select first draft (index 1 because 0 is select all)

      expect(screen.getByText('Delete Selected (1)')).toBeInTheDocument();
    });
  });

  it('handles select all drafts', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(async () => {
      const selectAllCheckbox = screen.getByLabelText('Select all drafts');
      await user.click(selectAllCheckbox);

      expect(screen.getByText('Delete Selected (2)')).toBeInTheDocument();
    });
  });

  it('handles draft deletion', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(async () => {
      const deleteButtons = screen.getAllByTitle('Delete draft');
      await user.click(deleteButtons[0]);

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'draftPosts',
        JSON.stringify([mockDrafts[1]]) // Should remove first draft
      );
    });
  });

  it('handles bulk deletion', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(async () => {
      // Select all drafts
      const selectAllCheckbox = screen.getByLabelText('Select all drafts');
      await user.click(selectAllCheckbox);

      // Delete selected
      const deleteSelectedButton = screen.getByText('Delete Selected (2)');
      await user.click(deleteSelectedButton);

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'draftPosts',
        JSON.stringify([]) // Should remove all drafts
      );
    });
  });

  it('handles draft publishing', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(async () => {
      const publishButtons = screen.getAllByText('Publish');
      await user.click(publishButtons[0]);

      expect(global.alert).toHaveBeenCalledWith('Draft published successfully!');
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'draftPosts',
        JSON.stringify([mockDrafts[1]]) // Should remove published draft
      );
    });
  });

  it('navigates to edit draft', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(async () => {
      const editButtons = screen.getAllByTitle('Edit draft');
      await user.click(editButtons[0]);

      expect(mockNavigate).toHaveBeenCalledWith('/create-post?draft=1');
    });
  });

  it('navigates to preview draft', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(async () => {
      const previewButtons = screen.getAllByTitle('Preview draft');
      await user.click(previewButtons[0]);

      expect(mockNavigate).toHaveBeenCalledWith('/preview-post?draft=1');
    });
  });

  it('navigates to schedule draft', async () => {
    const user = userEvent.setup();
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(async () => {
      const scheduleButtons = screen.getAllByTitle('Schedule draft');
      await user.click(scheduleButtons[0]);

      expect(mockNavigate).toHaveBeenCalledWith('/schedule-post?draft=1');
    });
  });

  it('formats dates correctly', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(() => {
      // Should show formatted creation and update dates
      expect(screen.getByText(/Created/)).toBeInTheDocument();
      expect(screen.getByText(/Updated/)).toBeInTheDocument();
    });
  });

  it('truncates long content', async () => {
    const longDraft = {
      ...mockDrafts[0],
      content: 'A'.repeat(200), // Very long content
    };

    localStorageMock.getItem.mockReturnValue(JSON.stringify([longDraft]));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(() => {
      const content = screen.getByText(/A+\.\.\./);
      expect(content).toBeInTheDocument();
    });
  });

  it('shows untitled for drafts without title', async () => {
    const untitledDraft = {
      ...mockDrafts[0],
      title: '',
    };

    localStorageMock.getItem.mockReturnValue(JSON.stringify([untitledDraft]));
    renderWithProviders(<DraftPostsPage />);

    await waitFor(() => {
      expect(screen.getByText('Untitled Draft')).toBeInTheDocument();
    });
  });

  it('has proper responsive design', () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockDrafts));
    const { container } = renderWithProviders(<DraftPostsPage />);

    const mainContainer = container.firstChild;
    expect(mainContainer).toHaveClass('max-w-4xl', 'mx-auto', 'p-4', 'pb-20');
  });

  it('loads mock data when localStorage is empty', async () => {
    localStorageMock.getItem.mockReturnValue(null);
    renderWithProviders(<DraftPostsPage />);

    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'draftPosts',
        expect.stringContaining('Tech Trends 2024')
      );
    });
  });
});
