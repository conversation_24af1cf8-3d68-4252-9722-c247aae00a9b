import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  EyeIcon,
  HeartIcon,
  ShareIcon,
  CalendarIcon,
  ClockIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';
import { Button } from '../ui/Button';

const BusinessDashboard = () => {
  const { user } = useSelector((state) => state.auth);
  const [timeRange, setTimeRange] = useState('7d');
  const [analytics, setAnalytics] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Mock analytics data
  useEffect(() => {
    const fetchAnalytics = async () => {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAnalytics({
        overview: {
          totalFollowers: 12543,
          totalPosts: 89,
          totalEngagement: 45678,
          totalReach: 123456,
          followerGrowth: 12.5,
          engagementRate: 8.3,
          reachGrowth: 15.2,
          postGrowth: 5.7
        },
        posts: [
          {
            id: 1,
            content: "Just launched our new product line! 🚀",
            date: '2024-01-15',
            views: 5432,
            likes: 234,
            shares: 45,
            comments: 67,
            engagement: 6.4
          },
          {
            id: 2,
            content: "Behind the scenes of our creative process",
            date: '2024-01-14',
            views: 3210,
            likes: 189,
            shares: 23,
            comments: 34,
            engagement: 7.7
          },
          {
            id: 3,
            content: "Customer success story that made our day! ❤️",
            date: '2024-01-13',
            views: 4567,
            likes: 345,
            shares: 78,
            comments: 89,
            engagement: 11.2
          }
        ],
        demographics: {
          ageGroups: [
            { range: '18-24', percentage: 25 },
            { range: '25-34', percentage: 35 },
            { range: '35-44', percentage: 22 },
            { range: '45-54', percentage: 12 },
            { range: '55+', percentage: 6 }
          ],
          locations: [
            { country: 'United States', percentage: 45 },
            { country: 'Canada', percentage: 15 },
            { country: 'United Kingdom', percentage: 12 },
            { country: 'Australia', percentage: 8 },
            { country: 'Germany', percentage: 7 },
            { country: 'Others', percentage: 13 }
          ]
        },
        engagement: {
          daily: [
            { date: '2024-01-09', likes: 45, comments: 12, shares: 8 },
            { date: '2024-01-10', likes: 67, comments: 18, shares: 12 },
            { date: '2024-01-11', likes: 89, comments: 25, shares: 15 },
            { date: '2024-01-12', likes: 123, comments: 34, shares: 22 },
            { date: '2024-01-13', likes: 156, comments: 45, shares: 28 },
            { date: '2024-01-14', likes: 134, comments: 38, shares: 25 },
            { date: '2024-01-15', likes: 178, comments: 52, shares: 35 }
          ]
        }
      });
      setIsLoading(false);
    };

    fetchAnalytics();
  }, [timeRange]);

  const StatCard = ({ title, value, change, icon: Icon, color = 'blue' }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-background rounded-lg border border-gray-800 p-6"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-400 mb-1">{title}</p>
          <p className="text-2xl font-bold text-white">{value}</p>
          {change !== undefined && (
            <div className={`flex items-center mt-2 text-sm ${
              change >= 0 ? 'text-green-400' : 'text-red-400'
            }`}>
              {change >= 0 ? (
                <ArrowUpIcon className="h-4 w-4 mr-1" />
              ) : (
                <ArrowDownIcon className="h-4 w-4 mr-1" />
              )}
              {Math.abs(change)}%
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg bg-${color}-500/20`}>
          <Icon className={`h-6 w-6 text-${color}-400`} />
        </div>
      </div>
    </motion.div>
  );

  const PostAnalytics = ({ post }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-background rounded-lg border border-gray-800 p-4"
    >
      <div className="flex items-start justify-between mb-3">
        <p className="text-white text-sm line-clamp-2">{post.content}</p>
        <span className="text-xs text-gray-400 ml-2">{post.date}</span>
      </div>
      
      <div className="grid grid-cols-4 gap-4 text-center">
        <div>
          <div className="flex items-center justify-center mb-1">
            <EyeIcon className="h-4 w-4 text-gray-400 mr-1" />
            <span className="text-sm font-medium text-white">{post.views.toLocaleString()}</span>
          </div>
          <p className="text-xs text-gray-400">Views</p>
        </div>
        
        <div>
          <div className="flex items-center justify-center mb-1">
            <HeartIcon className="h-4 w-4 text-red-400 mr-1" />
            <span className="text-sm font-medium text-white">{post.likes}</span>
          </div>
          <p className="text-xs text-gray-400">Likes</p>
        </div>
        
        <div>
          <div className="flex items-center justify-center mb-1">
            <ShareIcon className="h-4 w-4 text-green-400 mr-1" />
            <span className="text-sm font-medium text-white">{post.shares}</span>
          </div>
          <p className="text-xs text-gray-400">Shares</p>
        </div>
        
        <div>
          <div className="flex items-center justify-center mb-1">
            <span className="text-sm font-medium text-primary-400">{post.engagement}%</span>
          </div>
          <p className="text-xs text-gray-400">Engagement</p>
        </div>
      </div>
    </motion.div>
  );

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-800 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-800 rounded-lg"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-64 bg-gray-800 rounded-lg"></div>
            <div className="h-64 bg-gray-800 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Business Analytics</h1>
          <p className="text-gray-400">Track your performance and grow your audience</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          
          <Button>
            Export Report
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Followers"
          value={analytics.overview.totalFollowers.toLocaleString()}
          change={analytics.overview.followerGrowth}
          icon={UserGroupIcon}
          color="blue"
        />
        <StatCard
          title="Total Posts"
          value={analytics.overview.totalPosts}
          change={analytics.overview.postGrowth}
          icon={ChartBarIcon}
          color="green"
        />
        <StatCard
          title="Engagement Rate"
          value={`${analytics.overview.engagementRate}%`}
          change={analytics.overview.engagementRate - 7.1}
          icon={HeartIcon}
          color="red"
        />
        <StatCard
          title="Total Reach"
          value={analytics.overview.totalReach.toLocaleString()}
          change={analytics.overview.reachGrowth}
          icon={TrendingUpIcon}
          color="purple"
        />
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Top Posts */}
        <div>
          <h2 className="text-xl font-semibold text-white mb-4">Top Performing Posts</h2>
          <div className="space-y-4">
            {analytics.posts.map((post) => (
              <PostAnalytics key={post.id} post={post} />
            ))}
          </div>
        </div>

        {/* Demographics */}
        <div>
          <h2 className="text-xl font-semibold text-white mb-4">Audience Demographics</h2>
          
          <div className="bg-background rounded-lg border border-gray-800 p-6 mb-6">
            <h3 className="text-lg font-medium text-white mb-4">Age Groups</h3>
            <div className="space-y-3">
              {analytics.demographics.ageGroups.map((group) => (
                <div key={group.range} className="flex items-center justify-between">
                  <span className="text-gray-300">{group.range}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-primary-500 h-2 rounded-full"
                        style={{ width: `${group.percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-400 w-8">{group.percentage}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-background rounded-lg border border-gray-800 p-6">
            <h3 className="text-lg font-medium text-white mb-4">Top Locations</h3>
            <div className="space-y-3">
              {analytics.demographics.locations.map((location) => (
                <div key={location.country} className="flex items-center justify-between">
                  <span className="text-gray-300">{location.country}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: `${location.percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-400 w-8">{location.percentage}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Engagement Chart Placeholder */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold text-white mb-4">Engagement Trends</h2>
        <div className="bg-background rounded-lg border border-gray-800 p-6">
          <div className="h-64 flex items-center justify-center text-gray-400">
            <div className="text-center">
              <ChartBarIcon className="h-12 w-12 mx-auto mb-2" />
              <p>Engagement chart would be displayed here</p>
              <p className="text-sm">Integration with charting library needed</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessDashboard;
